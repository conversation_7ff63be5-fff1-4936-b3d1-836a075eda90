import { useState, useEffect } from "react";
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
} from "../components/ui/card";
import { But<PERSON> } from "../components/ui/button";
import { Input } from "../components/ui/input";
import {
  useDeleteProject,
  useDuplicateProject,
} from "../api/projectManagementApi";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../components/ui/select";
import { Badge } from "../components/ui/badge";
import { Progress } from "../components/ui/progress";
import { toast } from "sonner";
import {
  Search,
  Plus,
  Trash2,
  Edit,
  Eye,
  TrendingUp,
  CheckCircle,
  AlertTriangle,
  Clock,
  Bookmark,
  Calendar,
  User,
  FileText,
  Link,
  Layers,
  FileStack,
  X,
} from "lucide-react";
import { useNavigation } from "../App";
//import useNavigation from "../components/Context/NavigationContext";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "../components/ui/dialog";

import { Label } from "../components/ui/label";
import { RadioGroup, RadioGroupItem } from "../components/ui/radio-group";
import InstructorProjectDetailsPage from "./InstructorProjectDetailsPage";
// import BitsDataScienceAPI from "../src/BitsDataScienceAPI"; // not needed after refactor
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../components/ui/table";
import {
  useEnhancedProjectsQuery,
  GetEnhancedProjectsParams,
  EnhancedProject,
} from "../api/projectManagementApi";
import {
  useLTIContextsQuery,
  useLTIContextMembersQuery,
  useUnassignedResourceLinksQuery,
  useLinkProjectMutation,
  LTIContext,
  LTIContextMember,
  LTIResourceLink,
} from "../api/ltiApi";
import { useNavigate } from "react-router-dom";
import paths from "../routes";

interface TemplateData {
  id: string;
  name: string;
  description: string;
  projectData: any;
  createdAt: string;
  createdBy: string;
  category: string;
  tags: string[];
  usageCount: number;
}

// Removed old Project interface (replaced by EnhancedProject)

// Removed unused Course interface (course shape available inside EnhancedProject)

// Removed old local Project interface (using EnhancedProject types now)

export default function ProjectManagementPage() {
  const { navigateTo } = useNavigation();
  const [activeTab, setActiveTab] = useState<
    "all-projects" | "templates" | "analytics"
  >("all-projects");
  const [templates, setTemplates] = useState<TemplateData[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  // const [categoryFilter, setCategoryFilter] = useState("all"); // unused (template category filter)
  const [isOpenCreate, setIsOpenCreate] = useState(false);
  const [isOpenSelectTemplate, setIsSelectTemplate] = useState(false);
  const [trackSelectTemplate, setTrackSelectTemplate] = useState("newProject");
  const [isProjectPreview, setIsProjectPreview] = useState(false);
  const [projectId, setProjectId] = useState<string>("");
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  // const api = useMemo(() => new BitsDataScienceAPI(""), []); // replaced by dedicated hook
  const [deleteProjectId, setDeleteProjectId] = useState<string | null>(null);
  const { mutate: duplicateProjectMutation, isPending } = useDuplicateProject();
  // Filters & pagination state for enhanced projects API
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [courseFilter, setCourseFilter] = useState<string | undefined>(
    undefined
  );
  const [statusFilter, setStatusFilter] = useState<string | undefined>(
    undefined
  );
  const [difficultyFilter, setDifficultyFilter] = useState<string | undefined>(
    undefined
  );

  const projectQueryParams: GetEnhancedProjectsParams = {
    page,
    limit,
    ...(searchQuery ? { search: searchQuery } : {}),
    ...(courseFilter ? { courseId: courseFilter } : {}),
    ...(statusFilter ? { status: statusFilter as any } : {}),
    ...(difficultyFilter ? { difficultyLevel: difficultyFilter as any } : {}),
  };

  const {
    data: enhancedProjectsData,
    isLoading: projectsLoading,
    isError: projectsError,
  } = useEnhancedProjectsQuery(projectQueryParams as GetEnhancedProjectsParams);

  const projects: EnhancedProject[] =
    (enhancedProjectsData as any)?.projects || [];
  const pagination = (enhancedProjectsData as any)?.pagination;

  // Mock projects data including Mall Customers Project parts
  // const mockProjects: Project[] = [
  //   {
  //     id: "linear-regression-1",
  //     name: "Linear Regression Analysis",
  //     course: "Data Science 101",
  //     instructor: "Dr. A. Sharma",
  //     status: "active",
  //     difficulty: "beginner",
  //     submissionStats: "A+",
  //     dueDate: "1/15/2025",
  //     statusNote: "Overdue",
  //   },
  //   {
  //     id: "mall-customers-part-1",
  //     name: "Mall Customers - Part 1: K-Means Clustering",
  //     course: "Machine Learning",
  //     instructor: "Dr. A. Sharma",
  //     status: "active",
  //     difficulty: "intermediate",
  //     submissionStats: "A+",
  //     dueDate: "1/20/2025",
  //     statusNote: "Due Soon",
  //   },
  //   {
  //     id: "mall-customers-part-2",
  //     name: "Mall Customers - Part 2: Cluster Labeling & Visualization",
  //     course: "Machine Learning",
  //     instructor: "Dr. A. Sharma",
  //     status: "active",
  //     difficulty: "intermediate",
  //     submissionStats: "A+",
  //     dueDate: "1/25/2025",
  //   },
  //   {
  //     id: "mall-customers-part-3",
  //     name: "Mall Customers - Part 3: kNN Prediction",
  //     course: "Machine Learning",
  //     instructor: "Dr. A. Sharma",
  //     status: "draft",
  //     difficulty: "advanced",
  //     submissionStats: "A+",
  //     dueDate: "1/30/2025",
  //   },
  //   {
  //     id: "neural-networks-1",
  //     name: "Neural Networks Implementation",
  //     course: "Deep Learning",
  //     instructor: "Dr. B. Patel",
  //     status: "completed",
  //     difficulty: "advanced",
  //     submissionStats: "A+",
  //     dueDate: "1/10/2025",
  //   },
  //   {
  //     id: "data-viz-dashboard",
  //     name: "Data Visualization Dashboard",
  //     course: "Data Analytics",
  //     instructor: "Prof. C. Singh",
  //     status: "active",
  //     difficulty: "intermediate",
  //     submissionStats: "A+",
  //     dueDate: "1/17/2025",
  //     statusNote: "Due Soon",
  //   },
  // ];

  const mockProjects: EnhancedProject[] = projects || [];

  // Load templates from localStorage on component mount
  useEffect(() => {
    try {
      const savedTemplates = localStorage.getItem("projectTemplates");
      if (savedTemplates) {
        const parsedTemplates = JSON.parse(savedTemplates);
        setTemplates(parsedTemplates);
      }
    } catch (error) {
      console.error("Failed to load templates:", error);
    }
  }, [activeTab]); // Reload when switching to templates tab

  // Sample templates for demo purposes if none exist
  useEffect(() => {
    if (templates.length === 0 && activeTab === "templates") {
      const sampleTemplates: TemplateData[] = [
        {
          id: "mall-customers-k-means-template",
          name: "Mall Customers K-Means Clustering Template",
          description:
            "Complete template for implementing k-means clustering on mall customer data with segmentation analysis",
          projectData: {
            projectName: "",
            projectDescription:
              "Implement k-means clustering algorithm to segment mall customers based on purchasing behavior",
            projectType: "individual",
            difficulty: "intermediate",
            notebookContent: `# Mall Customers K-Means Clustering

## Objectives
- Implement k-means clustering from scratch
- Apply clustering to mall customer dataset
- Analyze customer segments
- Visualize clustering results

## Dataset
Mall customer data with features:
- Age
- Annual Income
- Spending Score

## Tasks
1. Load and explore the dataset
2. Implement k-means algorithm
3. Determine optimal number of clusters (elbow method)
4. Apply clustering and analyze segments
5. Create visualizations

## Expected Deliverables
- Working k-means implementation
- Customer segmentation analysis
- Cluster visualization plots
- Business insights report`,
            checkpoints: [
              {
                id: "1",
                name: "Data Exploration",
                description: "Load data and perform exploratory analysis",
                startDate: "",
                endDate: "",
              },
              {
                id: "2",
                name: "K-Means Implementation",
                description: "Implement k-means clustering algorithm",
                startDate: "",
                endDate: "",
              },
              {
                id: "3",
                name: "Cluster Analysis",
                description: "Apply clustering and analyze customer segments",
                startDate: "",
                endDate: "",
              },
              {
                id: "4",
                name: "Visualization",
                description: "Create comprehensive cluster visualizations",
                startDate: "",
                endDate: "",
              },
            ],
            gradingRubric: [
              {
                checkpoint: "Algorithm Implementation",
                criteria:
                  "Correct k-means implementation with proper initialization",
                maxMarks: 30,
              },
              {
                checkpoint: "Data Analysis",
                criteria:
                  "Thorough exploratory data analysis and preprocessing",
                maxMarks: 25,
              },
              {
                checkpoint: "Clustering Results",
                criteria:
                  "Optimal cluster selection and meaningful segmentation",
                maxMarks: 25,
              },
              {
                checkpoint: "Visualization & Insights",
                criteria: "Clear visualizations and business insights",
                maxMarks: 20,
              },
            ],
            maxSubmissions: "3",
            allowLateSubmissions: true,
            datasets: ["mall_customers.csv"],
          },
          createdAt: "2024-12-15T10:00:00Z",
          createdBy: "Dr. A. Sharma",
          category: "intermediate",
          tags: [
            "clustering",
            "k-means",
            "customer-segmentation",
            "unsupervised-learning",
          ],
          usageCount: 12,
        },
        {
          id: "mall-customers-labeling-template",
          name: "Mall Customers Cluster Labeling Template",
          description:
            "Template for labeling customer clusters and creating advanced visualizations with business interpretations",
          projectData: {
            projectName: "",
            projectDescription:
              "Label customer clusters with meaningful names and create comprehensive visualizations",
            projectType: "individual",
            difficulty: "intermediate",
            notebookContent: `# Mall Customers Cluster Labeling & Visualization

## Objectives
- Use clustering results from Part 1
- Label clusters with meaningful business names
- Create advanced visualizations
- Develop customer personas

## Prerequisites
- Completed Part 1 (K-Means Clustering)
- Customer cluster assignments

## Tasks
1. Analyze cluster characteristics
2. Assign meaningful labels to clusters
3. Create customer personas
4. Build interactive visualizations
5. Generate business recommendations

## Expected Deliverables
- Labeled customer segments
- Customer persona profiles
- Interactive dashboard
- Business strategy recommendations`,
            checkpoints: [
              {
                id: "1",
                name: "Cluster Analysis",
                description: "Analyze cluster characteristics and patterns",
                startDate: "",
                endDate: "",
              },
              {
                id: "2",
                name: "Cluster Labeling",
                description: "Assign meaningful names to customer segments",
                startDate: "",
                endDate: "",
              },
              {
                id: "3",
                name: "Persona Development",
                description: "Create detailed customer personas",
                startDate: "",
                endDate: "",
              },
              {
                id: "4",
                name: "Visualization Dashboard",
                description: "Build interactive visualization dashboard",
                startDate: "",
                endDate: "",
              },
            ],
            gradingRubric: [
              {
                checkpoint: "Cluster Interpretation",
                criteria: "Accurate analysis of cluster characteristics",
                maxMarks: 25,
              },
              {
                checkpoint: "Labeling Quality",
                criteria: "Meaningful and business-relevant cluster labels",
                maxMarks: 25,
              },
              {
                checkpoint: "Visualization Design",
                criteria: "Professional and insightful visualizations",
                maxMarks: 30,
              },
              {
                checkpoint: "Business Insights",
                criteria: "Actionable business recommendations",
                maxMarks: 20,
              },
            ],
            maxSubmissions: "2",
            allowLateSubmissions: true,
            datasets: ["mall_customers.csv", "cluster_results.csv"],
          },
          createdAt: "2024-12-16T14:30:00Z",
          createdBy: "Dr. A. Sharma",
          category: "intermediate",
          tags: [
            "visualization",
            "customer-personas",
            "business-analysis",
            "clustering",
          ],
          usageCount: 8,
        },
        {
          id: "mall-customers-knn-template",
          name: "Mall Customers kNN Prediction Template",
          description:
            "Template for implementing k-nearest neighbors prediction using labeled customer clusters",
          projectData: {
            projectName: "",
            projectDescription:
              "Implement kNN algorithm to predict customer segments for new customers using labeled cluster data",
            projectType: "individual",
            difficulty: "advanced",
            notebookContent: `# Mall Customers kNN Prediction

## Objectives
- Implement k-nearest neighbors algorithm
- Use labeled clusters for prediction
- Evaluate model performance
- Build prediction pipeline

## Prerequisites
- Completed Part 1 (K-Means Clustering)
- Completed Part 2 (Cluster Labeling)
- Labeled customer dataset

## Tasks
1. Prepare labeled training dataset
2. Implement kNN algorithm from scratch
3. Optimize k parameter using cross-validation
4. Build prediction pipeline
5. Evaluate model performance
6. Create prediction interface

## Expected Deliverables
- Complete kNN implementation
- Model performance evaluation
- Prediction pipeline
- Interactive prediction tool`,
            checkpoints: [
              {
                id: "1",
                name: "Data Preparation",
                description: "Prepare labeled dataset for training",
                startDate: "",
                endDate: "",
              },
              {
                id: "2",
                name: "kNN Implementation",
                description: "Implement k-nearest neighbors algorithm",
                startDate: "",
                endDate: "",
              },
              {
                id: "3",
                name: "Model Optimization",
                description: "Optimize k parameter and validate model",
                startDate: "",
                endDate: "",
              },
              {
                id: "4",
                name: "Prediction System",
                description: "Build complete prediction pipeline",
                startDate: "",
                endDate: "",
              },
            ],
            gradingRubric: [
              {
                checkpoint: "Algorithm Implementation",
                criteria:
                  "Correct kNN implementation with proper distance metrics",
                maxMarks: 35,
              },
              {
                checkpoint: "Model Validation",
                criteria: "Proper cross-validation and parameter optimization",
                maxMarks: 25,
              },
              {
                checkpoint: "Performance Evaluation",
                criteria: "Comprehensive model evaluation metrics",
                maxMarks: 25,
              },
              {
                checkpoint: "Pipeline Integration",
                criteria: "Complete end-to-end prediction system",
                maxMarks: 15,
              },
            ],
            maxSubmissions: "2",
            allowLateSubmissions: false,
            datasets: ["mall_customers.csv", "labeled_customers.csv"],
          },
          createdAt: "2024-12-17T09:15:00Z",
          createdBy: "Dr. A. Sharma",
          category: "advanced",
          tags: [
            "knn",
            "supervised-learning",
            "prediction",
            "model-evaluation",
          ],
          usageCount: 5,
        },
        {
          id: "sample-1",
          name: "Basic ML Classification Template",
          description:
            "A starter template for building classification models with common datasets",
          projectData: {
            projectName: "",
            projectDescription:
              "Basic machine learning classification project template",
            projectType: "group",
            difficulty: "beginner",
            notebookContent: `# Machine Learning Classification Project

## Objectives
- Build a classification model
- Evaluate model performance
- Visualize results

## Getting Started
1. Load the dataset
2. Explore data characteristics
3. Preprocess the data
4. Train multiple models
5. Compare results

## Expected Deliverables
- Trained classification model
- Performance evaluation report
- Data visualization charts`,
            checkpoints: [
              {
                id: "1",
                name: "Data Exploration",
                description: "Analyze dataset and identify patterns",
                startDate: "",
                endDate: "",
              },
              {
                id: "2",
                name: "Model Training",
                description: "Train and tune classification models",
                startDate: "",
                endDate: "",
              },
              {
                id: "3",
                name: "Evaluation",
                description: "Evaluate model performance and document results",
                startDate: "",
                endDate: "",
              },
            ],
            gradingRubric: [
              {
                checkpoint: "Data Exploration",
                criteria: "Thorough analysis and visualization",
                maxMarks: 30,
              },
              {
                checkpoint: "Model Implementation",
                criteria: "Correct implementation and tuning",
                maxMarks: 40,
              },
              {
                checkpoint: "Evaluation & Documentation",
                criteria: "Clear evaluation and professional documentation",
                maxMarks: 30,
              },
            ],
            maxSubmissions: "3",
            allowLateSubmissions: true,
            datasets: [],
          },
          createdAt: "2024-12-01T10:00:00Z",
          createdBy: "Dr. A. Sharma",
          category: "beginner",
          tags: ["classification", "machine-learning", "python"],
          usageCount: 15,
        },
        {
          id: "sample-2",
          name: "Advanced NLP Pipeline",
          description:
            "Complete natural language processing pipeline with sentiment analysis",
          projectData: {
            projectName: "",
            projectDescription:
              "Advanced NLP project with text preprocessing, feature extraction, and sentiment analysis",
            projectType: "individual",
            difficulty: "advanced",
            notebookContent: `# Advanced NLP Pipeline Project

## Objectives
- Build end-to-end NLP pipeline
- Implement sentiment analysis
- Deploy model for inference

## Getting Started
1. Text preprocessing and cleaning
2. Feature extraction (TF-IDF, embeddings)
3. Model training and evaluation
4. Pipeline deployment

## Expected Deliverables
- Complete NLP pipeline
- Sentiment analysis model
- Deployment demo`,
            checkpoints: [
              {
                id: "1",
                name: "Text Preprocessing",
                description: "Clean and prepare text data",
                startDate: "",
                endDate: "",
              },
              {
                id: "2",
                name: "Feature Engineering",
                description: "Extract meaningful features from text",
                startDate: "",
                endDate: "",
              },
              {
                id: "3",
                name: "Model Development",
                description: "Build and train NLP models",
                startDate: "",
                endDate: "",
              },
              {
                id: "4",
                name: "Pipeline Deployment",
                description: "Deploy working pipeline",
                startDate: "",
                endDate: "",
              },
            ],
            gradingRubric: [
              {
                checkpoint: "Preprocessing Quality",
                criteria: "Effective text cleaning and preparation",
                maxMarks: 25,
              },
              {
                checkpoint: "Feature Engineering",
                criteria: "Innovative feature extraction techniques",
                maxMarks: 25,
              },
              {
                checkpoint: "Model Performance",
                criteria: "High-performing sentiment analysis model",
                maxMarks: 30,
              },
              {
                checkpoint: "Pipeline Implementation",
                criteria: "Working end-to-end pipeline",
                maxMarks: 20,
              },
            ],
            maxSubmissions: "2",
            allowLateSubmissions: false,
            datasets: [],
          },
          createdAt: "2024-11-15T14:30:00Z",
          createdBy: "Dr. A. Sharma",
          category: "advanced",
          tags: ["nlp", "sentiment-analysis", "advanced"],
          usageCount: 8,
        },
      ];
      setTemplates(sampleTemplates);
    }
  }, [templates.length, activeTab]);

  const handleUseTemplate = (template: TemplateData) => {
    navigateTo("use-template", { templateData: template });
  };

  const handleDeleteTemplate = (templateId: string) => {
    try {
      const updatedTemplates = templates.filter((t) => t.id !== templateId);
      setTemplates(updatedTemplates);
      localStorage.setItem(
        "projectTemplates",
        JSON.stringify(updatedTemplates)
      );
      toast.success("Template deleted successfully");
    } catch (error) {
      toast.error("Failed to delete template");
    }
  };

  const handleDuplicateTemplate = (template: TemplateData) => {
    try {
      const duplicatedTemplate = {
        ...template,
        id: Date.now().toString(),
        name: `${template.name} (Copy)`,
        createdAt: new Date().toISOString(),
        usageCount: 0,
      };
      const updatedTemplates = [...templates, duplicatedTemplate];
      setTemplates(updatedTemplates);
      localStorage.setItem(
        "projectTemplates",
        JSON.stringify(updatedTemplates)
      );
      toast.success("Template duplicated successfully");
    } catch (error) {
      toast.error("Failed to duplicate template");
    }
  };

  // Filter templates based on search and category
  const filteredTemplates = templates.filter((template) => {
    const matchesSearch =
      template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      template.description.toLowerCase().includes(searchQuery.toLowerCase());
    // Removed category filter for templates (was unused in UI)
    return matchesSearch;
  });

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return (
          <Badge className="bg-bits-completed rounded-full text-bits-green text-xs px-2 py-1">
            Active
          </Badge>
        );
      case "completed":
        return (
          <Badge className="bg-bits-blue/10 rounded-full text-bits-blue text-xs px-2 py-1">
            Completed
          </Badge>
        );
      case "draft":
        return (
          <Badge className="bg-muted rounded-full text-bits-grey-600 text-xs px-2 py-1">
            Draft
          </Badge>
        );
      default:
        return (
          <Badge variant="outline" className="text-xs px-2 py-1">
            {status}
          </Badge>
        );
    }
  };

  const getDifficultyBadge = (difficulty: string) => {
    switch (difficulty) {
      case "beginner":
        return (
          <Badge className="bg-bits-completed rounded-full text-bits-green text-xs px-2 py-1">
            Beginner
          </Badge>
        );
      case "intermediate":
        return (
          <Badge className="text-bits-blue rounded-full border-bits-blue text-xs px-2 py-1">
            Intermediate
          </Badge>
        );
      case "advanced":
        return (
          <Badge className="text-bits-red rounded-full border-bits-red text-xs px-2 py-1">
            Advanced
          </Badge>
        );
      default:
        return (
          <Badge variant="outline" className="text-xs px-2 py-1">
            {difficulty}
          </Badge>
        );
    }
  };

  // const getStatusNote = (statusNote?: string) => { /* unused retained for future */ };

  const StatCard = ({ title, value }: { title: string; value: string }) => (
    <Card className="border border-border">
      <CardContent className="p-6">
        <div className="text-3xl font-bold text-bits-blue-neutral-900  mb-1">
          {value}
        </div>
        <div className="text-sm text-bits-grey-600">{title}</div>
      </CardContent>
    </Card>
  );

  const AllProjectsTab = () => {
    const handleDelete = async () => {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 2000));

      setIsDeleteModalOpen(false);
      alert("Project deleted successfully!");
    };
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-medium text-bits-grey">Projects</h2>
          <div className="flex items-center gap-4">
            <div className="relative flex-1 w-80">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400 bg-bits-grey-100" />
              <Input
                placeholder="Search Projects..."
                className="pl-10  bg-bits-grey-100"
                value={searchQuery}
                onChange={(e) => {
                  setSearchQuery(e.target.value);
                  setPage(1);
                }}
              />
            </div>
            <Select
              value={courseFilter || "all-courses"}
              onValueChange={(v) => {
                setCourseFilter(v === "all-courses" ? undefined : v);
                setPage(1);
              }}
            >
              <SelectTrigger className="w-40  bg-bits-grey-100">
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="text-md ">
                <SelectItem value="all-courses">All courses</SelectItem>
                {/* Dynamic course options could go here */}
              </SelectContent>
            </Select>
            <Select
              value={statusFilter || "all-status"}
              onValueChange={(v) => {
                setStatusFilter(v === "all-status" ? undefined : v);
                setPage(1);
              }}
            >
              <SelectTrigger className="w-32 bg-bits-grey-100">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all-status">All status</SelectItem>
                <SelectItem value="draft">Draft</SelectItem>
                <SelectItem value="published">Published</SelectItem>
                <SelectItem value="archived">Archived</SelectItem>
              </SelectContent>
            </Select>
            <Select
              value={difficultyFilter || "all-difficulties"}
              onValueChange={(v) => {
                setDifficultyFilter(v === "all-difficulties" ? undefined : v);
                setPage(1);
              }}
            >
              <SelectTrigger className="w-40 bg-bits-grey-100">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all-difficulties">
                  All Difficulties
                </SelectItem>
                <SelectItem value="beginner">Beginner</SelectItem>
                <SelectItem value="intermediate">Intermediate</SelectItem>
                <SelectItem value="advanced">Advanced</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="bg-white border border-gray-200 rounded-lg">
          <Table className="text-sm">
            <TableHeader>
              <TableRow className="bg-gray-50">
                <TableHead className="text-sm font-medium text-bits-grey">
                  ID
                </TableHead>
                <TableHead className="text-sm font-medium text-bits-grey">
                  Name
                </TableHead>
                <TableHead className="text-sm font-medium text-bits-grey">
                  Course
                </TableHead>
                <TableHead className="text-sm font-medium text-bits-grey">
                  Status
                </TableHead>
                <TableHead className="text-sm font-medium text-bits-grey">
                  Difficulty
                </TableHead>
                <TableHead className="text-sm font-medium text-bits-grey">
                  Submissions
                </TableHead>
                <TableHead className="text-sm font-medium text-bits-grey">
                  Due Date
                </TableHead>
                <TableHead className="text-sm font-medium text-bits-grey">
                  Actions
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {projectsLoading && (
                <TableRow>
                  <TableCell
                    colSpan={8}
                    className="text-center py-6 text-bits-grey-600"
                  >
                    Loading projects...
                  </TableCell>
                </TableRow>
              )}
              {projectsError && !projectsLoading && (
                <TableRow>
                  <TableCell
                    colSpan={8}
                    className="text-center py-6 text-bits-red text-sm"
                  >
                    Failed to load projects
                  </TableCell>
                </TableRow>
              )}
              {!projectsLoading &&
                !projectsError &&
                mockProjects.length === 0 && (
                  <TableRow>
                    <TableCell
                      colSpan={8}
                      className="text-center py-6 text-bits-grey-600 text-sm"
                    >
                      No projects found
                    </TableCell>
                  </TableRow>
                )}

              {mockProjects.map((project) => (
                <TableRow
                  key={project.id}
                  className="text-md font-normal text-bits-grey-600"
                >
                  <TableCell className="text-md font-normal text-bits-grey-600 max-w-[160px] truncate">
                    {project.projectId}
                  </TableCell>
                  <TableCell className="text-md font-normal text-bits-grey-600">
                    {project.title}
                  </TableCell>
                  <TableCell className="text-md font-normal text-bits-grey-600">
                    {project.course?.name}
                  </TableCell>
                  <TableCell className="capitalize">
                    {getStatusBadge(project.status)}
                  </TableCell>
                  <TableCell>
                    {getDifficultyBadge(project.difficultyLevel)}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center justify-center w-10 text-sm text-bits-grey-700 bg-bits-grey-100 rounded-full">
                      {project.submissionCount}
                    </div>
                  </TableCell>
                  <TableCell className="text-sm text-bits-grey-900">
                    {project.dueDate || "-"}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0"
                        onClick={() => {
                          navigate(
                            paths.instructorProjectDetails.replace(
                              ":projectId",
                              project.id
                            )
                          );
                        }}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0"
                        onClick={() => setIsModalOpen(true)}
                      >
                        <Link className="h-4 w-4" />
                      </Button>
                      <LinkProjectModal
                        isOpen={isModalOpen}
                        setIsOpen={setIsModalOpen}
                        projectId={project.id}
                        // platformId can be omitted since it's optional
                      />
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0"
                        title="Copy Project"
                        onClick={() => duplicateProjectMutation(project.id)}
                      >
                        <FileStack className="h-4 w-4" />
                      </Button>

                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0"
                        onClick={() => setDeleteProjectId(project.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>

                      {deleteProjectId && (
                        <DeleteProjectModal
                          isOpen={!!deleteProjectId}
                          onClose={() => setDeleteProjectId(null)}
                          projectId={deleteProjectId}
                          projectName={
                            mockProjects.find((p) => p.id === deleteProjectId)
                              ?.title ?? "Untitled Project"
                          }
                        />
                      )}

                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0"
                        onClick={() => {
                          navigate(
                            `${paths.editProject.replace(
                              ":projectId",
                              project.id
                            )}?mode=edit`
                          );
                          //navigateTo("edit-project", { projectId: project.id })
                        }}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        {/* Pagination */}
        {pagination && (
          <div className="flex items-center justify-between pt-2">
            <div className="text-sm text-bits-grey-600">
              Page {pagination.currentPage} of {pagination.totalPages} •{" "}
              {pagination.totalItems} items
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                disabled={page <= 1 || projectsLoading}
                onClick={() => setPage((p) => Math.max(1, p - 1))}
              >
                Prev
              </Button>
              <Button
                variant="outline"
                size="sm"
                disabled={page >= pagination.totalPages || projectsLoading}
                onClick={() => setPage((p) => p + 1)}
              >
                Next
              </Button>
              <Select
                value={String(limit)}
                onValueChange={(v) => {
                  setLimit(Number(v));
                  setPage(1);
                }}
              >
                <SelectTrigger className="w-24 bg-bits-grey-100">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {[10, 20, 30, 50].map((l) => (
                    <SelectItem key={l} value={String(l)}>
                      {l} / page
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        )}
      </div>
    );
  };

  const TemplatesTab = () => (
    <div className="space-y-6">
      {/* <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-900">Project Templates</h2>
        <Button
          className="bg-blue-600 hover:bg-blue-700"
          onClick={() => navigateTo('create-project')}
        >
          <Plus className="h-4 w-4 mr-2" />
          Create Template
        </Button>
      </div> */}

      {/* Template Filters */}
      {/* <div className="flex items-center gap-4">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search Templates..."
            className="pl-10 bg-white"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <Select value={categoryFilter} onValueChange={setCategoryFilter}>
          <SelectTrigger className="w-40 bg-white">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Categories</SelectItem>
            <SelectItem value="beginner">Beginner</SelectItem>
            <SelectItem value="intermediate">Intermediate</SelectItem>
            <SelectItem value="advanced">Advanced</SelectItem>
          </SelectContent>
        </Select>
      </div> */}

      {/* Templates Grid */}
      <Card className="p-4">
        <h2 className="text-lg font-medium text-bits-grey">
          Project Templates
        </h2>
        <div className="grid grid-cols-1 lg:grid-cols-1 gap-6">
          {filteredTemplates.map((template) => (
            <Card key={template.id} className="bg-white border border-gray-200">
              <CardContent className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <div className="flex items-center justify-between  mb-2">
                      <h3 className="text-xl font-medium">{template.name}</h3>
                      {getStatusBadge("active")}
                    </div>
                    <p className="text-sm text-bits-grey-600 mb-3">
                      {template.id}
                    </p>
                    <p className="text-sm text-bits-grey-600 mb-3">
                      {template.description}
                    </p>
                    <div className="flex items-center gap-4 text-sm text-bits-grey-600 mb-4">
                      <div className="flex items-center gap-1">
                        <User className="h-3 w-3" />
                        <span>Created by {template.createdBy}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        <span>
                          Last used:{" "}
                          {new Date(template.createdAt).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                    <div className="flex items-center gap-2  justify-between">
                      <div className="flex gap-1">
                        {template.tags.map((tag, index) => (
                          <Badge
                            key={index}
                            variant="outline"
                            className="text-xs text-bits-grey px-2 py-1"
                          >
                            {tag}
                          </Badge>
                        ))}
                      </div>
                      <div className="flex items-center justify-end gap-1">
                        <span className="text-lg font-medium text-bits-grey-600">
                          Used {template.usageCount} times
                        </span>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDuplicateTemplate(template)}
                            className="text-bits-grey-700"
                          >
                            <Eye className="h-4 w-4 mr-1" />
                            Preview
                          </Button>
                          {/* <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDuplicateTemplate(template)}
                          >
                            <Copy className="h-4 w-4 mr-1" />
                            Duplicate
                          </Button> */}
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteTemplate(template.id)}
                            className="text-bits-grey-700 "
                          >
                            <Edit className="h-4 w-4 mr-1" />
                            Edit
                          </Button>
                          {/* <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteTemplate(template.id)}
                            className="text-red-600 hover:text-red-700 hover:bg-red-50"
                          >
                            <Trash2 className="h-4 w-4 mr-1" />
                            Delete
                          </Button> */}
                          <Button
                            className="bg-bits-blue"
                            size="sm"
                            onClick={() => handleUseTemplate(template)}
                          >
                            {/* <Bookmark className="h-4 w-4 mr-1" /> */}
                            Use Template
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </Card>

      {/* Empty State */}
      {filteredTemplates.length === 0 && (
        <Card className="bg-white border border-gray-200">
          <CardContent className="p-12 text-center">
            <Bookmark className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {searchQuery ? "No templates found" : "No templates yet"}
            </h3>
            <p className="text-gray-600 mb-6">
              {searchQuery
                ? "Try adjusting your search or filter criteria."
                : "Create your first project template to reuse configurations."}
            </p>
            <Button
              className="bg-blue-600 hover:bg-blue-700"
              onClick={() => navigateTo("create-project")}
            >
              <Plus className="h-4 w-4 mr-2" />
              Create Template
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );

  const AnalyticsTab = () => (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <Card className="bg-white border border-gray-200">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center font-medium gap-2 text-lg">
            <TrendingUp className="h-5 w-5 text-bits-blue" />
            <span className="text-lg font-medium text-bits-grey">
              Project Completion Trends
            </span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center mb-6">
            <div className="text-3xl font-bold text-bits-green mb-2">85%</div>
            <div className="text-sm text-bits-grey-600">
              Average completion rate
            </div>
          </div>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">This Month</span>
              <span className="text-sm font-normal text-bits-grey-600">
                92%
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Last Month</span>
              <span className="text-sm font-normal text-bits-grey-600">
                88%
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">3 Months Ago</span>
              <span className="text-sm font-normal text-bits-grey-600">
                76%
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="bg-white border border-gray-200">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center font-medium gap-2 text-lg">
            <Layers className="h-5 w-5 text-bits-warning" />
            <span className="text-lg font-medium text-bits-grey">
              Popular Technologies
            </span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Python */}
            <div className="flex justify-between items-center">
              <span className="text-md font-normal text-bits-grey-600">
                Python
              </span>
              <div className="flex items-center gap-2 w-[40%]">
                <Progress
                  value={95}
                  className="h-2 text-xs font-normal text-bits-grey-600"
                />
                <span className="text-sm text-bits-grey-600">95%</span>
              </div>
            </div>

            {/* Scikit-learn */}
            <div className="flex justify-between items-center">
              <span className="text-md font-normal text-bits-grey-600">
                Scikit-learn
              </span>
              <div className="flex items-center gap-2 w-[40%]">
                <Progress
                  value={85}
                  className="h-2 text-xs font-normal text-bits-grey-600"
                />
                <span className="text-sm text-bits-grey-600">85%</span>
              </div>
            </div>

            {/* Pandas */}
            <div className="flex justify-between items-center">
              <span className="text-md font-normal text-bits-grey-600">
                Pandas
              </span>
              <div className="flex items-center gap-2 w-[40%]">
                <Progress
                  value={92}
                  className="h-2 text-xs font-normal text-bits-grey-600"
                />
                <span className="text-sm text-bits-grey-600">92%</span>
              </div>
            </div>

            {/* TensorFlow */}
            <div className="flex justify-between items-center">
              <span className="text-md font-normal text-bits-grey-600">
                TensorFlow
              </span>
              <div className="flex items-center gap-2 w-[40%]">
                <Progress
                  value={68}
                  className="h-2 text-xs font-normal text-bits-grey-600"
                />
                <span className="text-sm text-bits-grey-600">68%</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="lg:col-span-2 bg-white border border-gray-200">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center font-medium gap-2 text-lg">
            <Clock className="h-5 w-5 text-bits-blue" />
            <span className="text-lg font-medium text-bits-grey">
              Recent Activity
            </span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-start gap-3 p-3 rounded-lg bg-bits-green-success-25">
              <div className="mt-1">
                <CheckCircle className="h-4 w-4 text-bits-green" />
              </div>
              <div className="flex-1">
                <div className="flex justify-between items-start">
                  <div className="">
                    <div className="text-md font-medium">Project completed</div>
                    <div className="text-sm font-normal text-bits-grey-600 mt-1">
                      Neural Networks Implementation - 20/20 students submitted
                    </div>
                  </div>
                  <span className="text-xs text-bits-grey-600">
                    2 hours ago
                  </span>
                </div>
              </div>
            </div>

            {/* <div className="flex items-start gap-3 p-3 rounded-lg ">
              <div className="mt-1">
                <Plus className="h-4 w-4 text-blue-600" />
              </div>
              <div className="flex-1">
                <div className="flex justify-between items-start">
                  <div>
                    <div className="text-sm font-medium text-gray-900">New Mall Customers projects created</div>
                    <div className="text-sm text-gray-600 mt-1">Dr. A. Sharma created 3-part Mall Customers ML series</div>
                  </div>
                  <span className="text-xs text-bits-grey-600">1 day ago</span>
                </div>
              </div>
            </div> */}

            <div className="flex items-start gap-3 p-3 rounded-lg bg-bits-warning-50 ">
              <div className="mt-1">
                <AlertTriangle className="h-4 w-4 text-bits-orange" />
              </div>
              <div className="flex-1">
                <div className="flex justify-between items-start">
                  <div>
                    <div className="text-md font-medium">
                      Deadline approaching
                    </div>
                    <div className="text-sm font-normal text-bits-grey-600 mt-1">
                      Mall Customers Part 1 due in 5 days - 42/55 submitted
                    </div>
                  </div>
                  <span className="text-xs text-bits-grey-600">
                    3 hours ago
                  </span>
                </div>
              </div>
            </div>

            <div className="flex items-start gap-3 p-3 rounded-lg bg-bits-blue-50 ">
              <div className="mt-1">
                <Plus className="h-4 w-4 text-bits-blue-500" />
              </div>
              <div className="flex-1">
                <div className="flex justify-between items-start">
                  <div>
                    <div className="text-md font-medium">
                      New template created
                    </div>
                    <div className="text-sm font-normal text-bits-grey-600 mt-1">
                      Dr. A. Sharma created "Advanced NLP Pipeline"
                    </div>
                  </div>
                  <span className="text-xs text-bits-grey-600">2 days ago</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  interface ShowSelectTemplateModalProps {
    isOpenSelectTemplate: boolean;
    setIsSelectTemplate: (open: boolean) => void;
    setTrackSelectTemplate: (val: any) => void;
  }

  const ShowSelectTemplateModal = ({
    isOpenSelectTemplate,
    setIsSelectTemplate,
    setTrackSelectTemplate,
  }: ShowSelectTemplateModalProps) => {
    const [selectedTemplate, setSelectedTemplate] = useState<number | null>(
      null
    );

    const templates = [
      { name: "House price prediction", createdAt: "18 May 2025" },
      { name: "Car price prediction", createdAt: "20 May 2025" },
      { name: "Stock market analysis", createdAt: "22 May 2025" },
      { name: "Customer churn model", createdAt: "25 May 2025" },
    ];

    return (
      <Dialog open={isOpenSelectTemplate} onOpenChange={setIsSelectTemplate}>
        <DialogContent className="sm:max-w-2xl bg-white border">
          <DialogHeader>
            <DialogTitle className="text-lg font-semibold">
              Select a Template
            </DialogTitle>
            <DialogDescription>
              Create a project from scratch or get started faster with a
              template.
            </DialogDescription>
          </DialogHeader>

          {/* Search & Sort */}
          <div className="flex gap-2">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search project template"
                className="pl-10 bg-bits-grey-100"
              />
            </div>
            <Select defaultValue="new-first">
              <SelectTrigger className="w-40 bg-bits-grey-100 flex-2">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="new-first">Newest First</SelectItem>
                <SelectItem value="later">Later</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Template Cards */}
          <Card className="grid grid-cols-1 md:grid-cols-2 gap-6 p-5">
            {templates?.map((i, index) => (
              <Card
                key={index}
                onClick={() => setSelectedTemplate(index)}
                className={`p-3 cursor-pointer transition border 
                ${
                  selectedTemplate === index
                    ? "border-bits-blue"
                    : "border-gray-200"
                }`}
              >
                <div className="flex items-center gap-5">
                  <FileText className="h-5 w-5 text-bits-blue" />
                  <div className="flex-col">
                    <CardTitle className="font-medium text-base">
                      {i.name}
                    </CardTitle>
                    <p className="text-sm text-bits-grey-600">
                      Created at {i.createdAt}
                    </p>
                  </div>
                </div>
              </Card>
            ))}
          </Card>

          {/* Footer Buttons */}
          <div className="flex gap-2">
            <Button
              onClick={() => setIsSelectTemplate(false)}
              className="flex-1"
              variant="outline"
            >
              Cancel
            </Button>

            <Button
              disabled={selectedTemplate === null}
              onClick={() => {
                console.log("Selected:", templates[selectedTemplate!]);
                setIsSelectTemplate(false);
                setTrackSelectTemplate(false); // optional
              }}
              className="flex-1 bg-bits-blue hover:bg-bits-blue"
            >
              Continue
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    );
  };

  interface CreateNewProjectModalProps {
    isOpenCreate: boolean;
    setIsOpenCreate: (open: boolean) => void;
    setTrackSelectTemplate: (val: any) => void;
    setIsSelectTemplate: (open: boolean) => void;
    trackSelectTemplate: string;
  }

  const CreateNewProjectModal = ({
    isOpenCreate,
    setIsOpenCreate,
    setTrackSelectTemplate,
    setIsSelectTemplate,
    trackSelectTemplate, // should be "newProject" | "useTemplate"
  }: CreateNewProjectModalProps) => {
    const handleClose = (open: boolean) => {
      if (!open) {
        // Reset everything when modal closes
        setIsOpenCreate(false);
        setTrackSelectTemplate("newProject");
        setIsSelectTemplate(false);
      } else {
        setIsOpenCreate(true);
      }
    };
    const navigate = useNavigate();
    return (
      <Dialog open={isOpenCreate} onOpenChange={handleClose}>
        <DialogContent className="max-w-sm bg-white border">
          <DialogHeader>
            <DialogTitle className="text-lg font-semibold">
              Create Project
            </DialogTitle>
            <DialogDescription>
              Create a project from scratch or get started faster with a
              template.
            </DialogDescription>
          </DialogHeader>

          <div className="flex">
            <RadioGroup
              value={trackSelectTemplate}
              onValueChange={(val) => setTrackSelectTemplate(val)}
              className="flex-col"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="newProject" id="newProject" />
                <Label
                  className="text-bits-grey-700 font-semibold text-lg"
                  htmlFor="newProject"
                >
                  New Project
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="useTemplate" id="useTemplate" />
                <Label
                  className="text-bits-grey-700 font-semibold text-lg"
                  htmlFor="useTemplate"
                >
                  Use Template
                </Label>
              </div>
            </RadioGroup>
          </div>

          <div className="flex gap-2">
            <Button
              onClick={() => handleClose(false)}
              className="flex-1"
              variant="outline"
            >
              Cancel
            </Button>

            <Button
              onClick={() => {
                if (trackSelectTemplate === "useTemplate") {
                  setIsOpenCreate(false);
                  setIsSelectTemplate(true);
                } else {
                  setIsOpenCreate(false);
                  navigate(`${paths.createProject}?mode=create`);
                }
              }}
              className="flex-1 bg-bits-blue hover:bg-bits-blue"
            >
              Continue
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    );
  };
  const navigate = useNavigate();
  interface LinkProjectModalProps {
    isOpen: boolean;
    setIsOpen: (open: boolean) => void;
    projectId: string;
    platformId?: string;
  }

  const LinkProjectModal = ({
    isOpen,
    setIsOpen,
    projectId,
    platformId,
  }: LinkProjectModalProps) => {
    const [selectedCourse, setSelectedCourse] = useState<string>("");
    const [selectedInstructors, setSelectedInstructors] = useState<string[]>(
      []
    );
    const [selectedAssignment, setSelectedAssignment] = useState<string>("");
    const [scoreMaximum, setScoreMaximum] = useState<number | null>(null);

    const handleClose = () => {
      setIsOpen(false);
      setSelectedCourse("");
      setSelectedInstructors([]);
      setSelectedAssignment("");
      setScoreMaximum(null);
    };

    // React Query hooks
    const { data: contexts = [], isLoading: contextsLoading } =
      useLTIContextsQuery(isOpen);

    // Get the selected context to extract platformId
    const selectedContext = (contexts as LTIContext[]).find(
      (context) => context.id === selectedCourse
    );
    const selectedPlatformId = selectedContext?.platformId || platformId;

    const { data: instructors = [], isLoading: instructorsLoading } =
      useLTIContextMembersQuery(
        {
          contextId: selectedCourse,
          platformId: selectedPlatformId,
          role: "instructor",
        },
        isOpen && !!selectedCourse && !!selectedPlatformId
      );

    const { data: assignments = [], isLoading: assignmentsLoading } =
      useUnassignedResourceLinksQuery(
        {
          contextId: selectedCourse,
          platformId: selectedPlatformId,
        },
        isOpen && !!selectedCourse && !!selectedPlatformId
      );

    const linkProjectMutation = useLinkProjectMutation();

    // Handle course selection
    const handleCourseChange = (courseId: string) => {
      setSelectedCourse(courseId);
      setSelectedInstructors([]);
      setSelectedAssignment("");
      setScoreMaximum(null);
    };

    // Handle assignment selection
    const handleAssignmentChange = (assignmentId: string) => {
      setSelectedAssignment(assignmentId);
      const assignment = (assignments as LTIResourceLink[]).find(
        (a) => a.id === assignmentId
      );
      const scoreMax = assignment?.lineItem?.scoreMaximum
        ? parseFloat(assignment.lineItem.scoreMaximum)
        : null;
      setScoreMaximum(scoreMax);
    };

    // Handle project linking
    const handleLinkProject = async () => {
      if (
        !selectedCourse ||
        !selectedAssignment ||
        selectedInstructors.length === 0
      ) {
        return;
      }

      try {
        await linkProjectMutation.mutateAsync({
          resourceLinkId: selectedAssignment,
          projectId,
          total_points: scoreMaximum || 0,
          instructor_ids: selectedInstructors,
        });

        toast.success("Project linked successfully!");
        handleClose();
      } catch (error) {
        toast.error("Failed to link project. Please try again.");
        console.error("Link project error:", error);
      }
    };

    return (
      <Dialog open={isOpen} onOpenChange={(open) => !open && handleClose()}>
        <DialogContent className="max-w-sm bg-white border transparent-overlay">
          <DialogHeader>
            <DialogTitle className="text-lg font-semibold">
              Link Project to LMS Assignment
            </DialogTitle>
          </DialogHeader>

          <div className="flex flex-col gap-4 mt-4">
            {/* Step 1: Course Selection */}
            <div>
              <Label htmlFor="course">Course *</Label>
              <Select
                value={selectedCourse}
                onValueChange={handleCourseChange}
                disabled={contextsLoading}
              >
                <SelectTrigger id="course" className="mt-2 w-full">
                  <SelectValue
                    placeholder={
                      contextsLoading ? "Loading courses..." : "Select Course"
                    }
                  />
                </SelectTrigger>
                <SelectContent>
                  {(contexts as LTIContext[]).map((context) => (
                    <SelectItem key={context.id} value={context.id}>
                      {context.contextTitle} ({context.contextLabel})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Step 2: Instructor Selection (only show after course is selected) */}
            {selectedCourse && (
              <div>
                <Label htmlFor="instructors">Instructors *</Label>
                <div className="mt-2 space-y-2">
                  {instructorsLoading ? (
                    <div className="text-sm text-gray-500">
                      Loading instructors...
                    </div>
                  ) : (
                    (instructors as LTIContextMember[]).map((instructor) => (
                      <div
                        key={instructor.id}
                        className="flex items-center space-x-2"
                      >
                        <input
                          type="checkbox"
                          id={`instructor-${instructor.id}`}
                          checked={selectedInstructors.includes(instructor.id)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setSelectedInstructors([
                                ...selectedInstructors,
                                instructor.id,
                              ]);
                            } else {
                              setSelectedInstructors(
                                selectedInstructors.filter(
                                  (id) => id !== instructor.id
                                )
                              );
                            }
                          }}
                          className="rounded border-gray-300"
                        />
                        <Label
                          htmlFor={`instructor-${instructor.id}`}
                          className="text-sm font-normal cursor-pointer"
                        >
                          {instructor.name} ({instructor.email})
                        </Label>
                      </div>
                    ))
                  )}
                </div>
              </div>
            )}

            {/* Step 3: Assignment Selection (only show after course is selected) */}
            {selectedCourse && (
              <div>
                <Label htmlFor="assignment">Assignment *</Label>
                <Select
                  value={selectedAssignment}
                  onValueChange={handleAssignmentChange}
                  disabled={assignmentsLoading}
                >
                  <SelectTrigger id="assignment" className="mt-2 w-full">
                    <SelectValue
                      placeholder={
                        assignmentsLoading
                          ? "Loading assignments..."
                          : "Select Assignment"
                      }
                    />
                  </SelectTrigger>
                  <SelectContent>
                    {(assignments as LTIResourceLink[]).map((assignment) => (
                      <SelectItem key={assignment.id} value={assignment.id}>
                        {assignment.resourceLinkTitle}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            {/* Step 4: Score Display (only show after assignment is selected) */}
            {selectedAssignment && scoreMaximum !== null && (
              <div>
                <Label htmlFor="score">Total Points</Label>
                <Input
                  id="score"
                  type="number"
                  value={scoreMaximum}
                  readOnly
                  className="mt-2 w-full bg-gray-50"
                />
              </div>
            )}
          </div>

          <div className="flex gap-2 mt-6">
            <Button onClick={handleClose} className="flex-1" variant="outline">
              Cancel
            </Button>
            <Button
              onClick={handleLinkProject}
              className="flex-1 bg-bits-blue hover:bg-bits-blue"
              disabled={
                !selectedCourse ||
                !selectedAssignment ||
                selectedInstructors.length === 0 ||
                linkProjectMutation.isPending
              }
            >
              {linkProjectMutation.isPending ? "Linking..." : "Link Project"}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    );
  };
  const DeleteProjectModal = ({
    isOpen,
    onClose,
    projectName = "Untitled Project",
    projectId,
  }) => {
    const { mutate: deleteProjectMutation, isPending } = useDeleteProject();

    const handleDelete = () => {
      deleteProjectMutation(projectId, {
        onSuccess: () => {
          toast.success("Project deleted successfully");
          onClose(); // close modal after success
        },
        onError: () => {
          onClose();
          toast.error("Failed to delete project");
        },
      });
    };

    return (
      <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
        <DialogContent className="max-w-md bg-white border transparent-overlay">
          <DialogHeader>
            <DialogTitle className="text-lg font-semibold text-black text-[18px]">
              Delete Project?
            </DialogTitle>
          </DialogHeader>

          <div className="mt-4">
            <p className="text-gray-700 mb-4 text-[14px]">
              Are you sure you want to delete{" "}
              <span className="font-semibold">{projectName}</span>?
            </p>
          </div>

          <div className="flex flex-row-reverse gap-4 mt-6">
            <button
              onClick={handleDelete}
              disabled={isPending}
              className="flex-1 px-4 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors font-medium"
            >
              {isPending ? "Deleting..." : "Delete"}
            </button>
            <button
              onClick={onClose}
              className="flex-1 px-4 py-3 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors font-medium"
            >
              Cancel
            </button>
          </div>
        </DialogContent>
      </Dialog>
    );
  };
  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <>
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-semibold">Project Management</h1>
            <p className="text-bits-grey-600 text-md mt-1">
              Manage all projects and templates across the platform
            </p>
          </div>
          <div className="flex items-center gap-3">
            {/* <Button variant="outline" className="bg-white">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button> */}
            <Button
              className="bg-bits-blue hover:bg-bits-blue-700"
              onClick={() => setIsOpenCreate(true)}
              //onClick={() => {navigate(paths.createProject);}}
            >
              <Plus className="h-4 w-4 mr-2" />
              <span className="text-sm font-semibold">
                {activeTab === "templates"
                  ? "Create Template"
                  : "Create Project"}
              </span>
            </Button>

            <CreateNewProjectModal
              isOpenCreate={isOpenCreate}
              setIsOpenCreate={setIsOpenCreate}
              setTrackSelectTemplate={setTrackSelectTemplate}
              setIsSelectTemplate={setIsSelectTemplate}
              trackSelectTemplate={trackSelectTemplate}
            />
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <StatCard title="Total Projects" value="6" />
          <StatCard title="Active Projects" value="4" />
          <StatCard title="Completed" value="1" />
          <StatCard title="Templates" value={templates.length.toString()} />
        </div>

        {/* Tabs */}
        <div className="flex items-center bg-gray-200 rounded-lg p-1 mb-6 w-fit">
          <button
            onClick={() => setActiveTab("all-projects")}
            className={`px-6 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === "all-projects"
                ? "bg-white text-gray-900 shadow-sm"
                : "text-gray-600 hover:text-gray-900"
            }`}
          >
            All Projects
          </button>
          <button
            onClick={() => setActiveTab("templates")}
            className={`px-6 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === "templates"
                ? "bg-white text-gray-900 shadow-sm"
                : "text-gray-600 hover:text-gray-900"
            }`}
          >
            Templates
          </button>
          <button
            onClick={() => setActiveTab("analytics")}
            className={`px-6 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === "analytics"
                ? "bg-white text-gray-900 shadow-sm"
                : "text-gray-600 hover:text-gray-900"
            }`}
          >
            Analytics
          </button>
        </div>

        {/* Tab Content */}
        {activeTab === "all-projects" && <AllProjectsTab />}
        {activeTab === "templates" && <TemplatesTab />}
        {activeTab === "analytics" && <AnalyticsTab />}
        {isOpenSelectTemplate && (
          <ShowSelectTemplateModal
            isOpenSelectTemplate={isOpenSelectTemplate}
            setIsSelectTemplate={setIsSelectTemplate}
            setTrackSelectTemplate={setTrackSelectTemplate}
          />
        )}
      </>
    </div>
  );
}
