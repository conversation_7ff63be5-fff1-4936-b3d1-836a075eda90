import React, {
  useState,
  useEffect,
  useRef,
  use<PERSON>allback,
  SetStateAction,
  useImperativeHandle,
  forwardRef,
} from "react";
import paths from "../routes";
import {
  ArrowLeft,
  Search,
  Plus,
  ChevronDown,
  ChevronRight,
  Folder,
  File,
  FileText,
  FileCode,
  Play,
  Square,
  RotateCcw,
  Trash2,
  Settings,
  Database,
  BookOpen,
  Save,
  Upload,
  CheckCircle,
  Edit,
  Clock,
  Copy,
  Check,
  Loader2,
  Download,
} from "lucide-react";
import { useNavigation } from "../App";
//import useNavigation from "../components/Context/NavigationContext";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import {
  useUpdateContentMutation,
  useExecuteCellMutation,
  useExecuteNotebookMutation,
  useRenameContentMutation,
  useDeleteContentMutation,
  useRestartKernelMutation,
  useInterruptKernelMutation,
  getContents,
} from "../api/jupyterManagementApi";
import { useJupyterWorkspace } from "../contexts/JupyterWorkspaceContext";
import { formatTimestamp } from "../helper/date-fns";
import { Button } from "../components/ui/button";
import { Badge } from "../components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "../components/ui/dropdown-menu";
import { toast } from "sonner";
import { Input } from "../components/ui/input";
import { useLocation, useNavigate, useParams } from "react-router-dom";

interface Cell {
  id: string;
  type: "code" | "markdown";
  content: string;
  output?: any[];
  execution_count?: number | null;
  metadata: any;
}

interface NotebookContentData {
  cells: Cell[];
  metadata: any;
  nbformat: number;
  nbformat_minor: number;
}

interface NotebookFile extends FileItem {
  type: "notebook";
  content: NotebookContentData;
}

interface Notebook {
  name: string;
  path: string;
  content: NotebookContentData;
}

interface FileItem {
  name: string;
  path: string;
  type: "file" | "directory" | "notebook";
  size?: number;
  last_modified?: string;
  mimetype?: string;
  content?: any;
}

// --- HELPER FUNCTIONS ---
const getFileIcon = (file: FileItem) => {
  if (file.type === "directory") {
    return <Folder size={16} className="text-yellow-500" />;
  }
  const ext = file?.name?.split(".").pop()?.toLowerCase();
  switch (ext) {
    case "ipynb":
      return <FileText size={16} className="text-orange-400" />;
    case "py":
      return <FileCode size={16} className="text-green-500" />;
    case "csv":
    case "json":
      return <FileText size={16} className="text-blue-400" />;
    default:
      return <File size={16} className="text-gray-400" />;
  }
};

const formatFileSize = (bytes?: number) => {
  if (!bytes) return "0 B";
  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(1))} ${sizes[i]}`;
};

const EDITABLE_EXTENSIONS = [
  "py",
  "r",
  "jl",
  "scala",
  "java",
  "c",
  "cpp",
  "h",
  "m",
  "sh",
  "js",
  "ts",
  "php",
  "rb",
  "pl",
  "txt",
  "md",
  "rst",
  "json",
  "yaml",
  "yml",
  "xml",
];

const isCsvFile = (file: FileItem | null): boolean => {
  if (!file?.name) return false;
  const extension = file?.name?.split(".").pop()?.toLowerCase();
  return extension === "csv";
};

const isEditableFile = (file: FileItem | null): boolean => {
  if (!file?.name) return false;
  const extension = file?.name?.split(".").pop()?.toLowerCase();
  return !!extension && EDITABLE_EXTENSIONS.includes(extension);
};

const formatTimeAgo = (dateString?: string) => {
  if (!dateString) return "";
  const date = new Date(dateString);
  const now = new Date();
  const seconds = Math.floor((now.getTime() - date.getTime()) / 1000);
  let interval = seconds / 86400;
  if (interval > 1) return `${Math.floor(interval)}d ago`;
  interval = seconds / 3600;
  if (interval > 1) return `${Math.floor(interval)}h ago`;
  interval = seconds / 60;
  if (interval > 1) return `${Math.floor(interval)}m ago`;
  return `${Math.floor(seconds)}s ago`;
};

function getUniqueFilename(
  existingNames: string[],
  baseName = "Untitled",
  ext = ".ipynb"
) {
  const existingSet = new Set(existingNames);
  let i = 0;
  let candidate = `${baseName}${ext}`;

  if (!existingSet.has(candidate)) return candidate;

  i = 1;
  while (true) {
    candidate = `${baseName}${i}${ext}`;
    if (!existingSet.has(candidate)) return candidate;
    i++;
  }
}

// --- CHILD COMPONENTS ---

const ProjectExplorer: React.FC<any> = ({
  files,
  onFileSelect,
  onFileCreate,
  onFileDelete,
  selectedFilePath,
  isDeleting,
  initialFile,
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [error, setError] = useState<string | null>(null);
  const [isCreatingFile, setIsCreatingFile] = useState(false);
  const [expandedSections, setExpandedSections] = useState({
    notebooks: true,
    datasets: true,
    pythonScripts: true,
  });

  const handleCreateNewFile = async (
    fileType: "notebook" | "markdown" | "text" | "python"
  ) => {
    if (isCreatingFile) return;

    setIsCreatingFile(true);
    setError(null);
    try {
      let ext = ".ipynb";
      if (fileType === "python") ext = ".py";
      if (fileType === "markdown") ext = ".md";
      if (fileType === "text") ext = ".txt";

      const existingNames = files.map((f: FileItem) => f.name);
      const newName = getUniqueFilename(existingNames, "Untitled", ext);

      await onFileCreate(fileType, newName);
    } catch (err) {
      console.error(`Failed to create new ${fileType} file:`, err);
      setError(`Failed to create ${fileType} file.`);
    } finally {
      setIsCreatingFile(false);
    }
  };

  const handleCopyPath = (e: React.MouseEvent, path: string) => {
    e.stopPropagation();
    navigator.clipboard
      .writeText(path)
      .then(() => toast.success("File path copied to clipboard!"))
      .catch((err) => {
        console.error("Failed to copy path:", err);
        toast.error("Failed to copy path.");
      });
  };

  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections((prev) => ({ ...prev, [section]: !prev[section] }));
  };

  const byRecent = (a: FileItem, b: FileItem) => {
    const ta = a.last_modified ? new Date(a.last_modified).getTime() : 0;
    const tb = b.last_modified ? new Date(b.last_modified).getTime() : 0;
    return tb - ta;
  };

  const isNotebookItem = (f: FileItem) =>
    f.mimetype === "application/x-ipynb+json" || f.name.endsWith(".ipynb");

  const isPythonItem = (f: FileItem) => f.name.endsWith(".py");

  const isDatasetItem = (f: FileItem) => !isNotebookItem(f) && !isPythonItem(f);

  const samePath = (a?: string, b?: string) => !!a && !!b && a === b;

  const filteredFiles = files.filter((f: FileItem) =>
    f.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Build full lists and sort by recent
  const notebooksAll = filteredFiles.filter(isNotebookItem).sort(byRecent);
  const pythonScriptsAll = filteredFiles.filter(isPythonItem).sort(byRecent);
  const datasetsAll = filteredFiles.filter(isDatasetItem).sort(byRecent);

  let notebooks = notebooksAll.slice(0, 5);
  let pythonScripts = pythonScriptsAll.slice(0, 5);
  let datasets = datasetsAll.slice(0, 5);

  if (initialFile && initialFile.path) {
    if (isNotebookItem(initialFile)) {
      if (
        !notebooks.some((f: FileItem) => samePath(f.path, initialFile.path))
      ) {
        notebooks = [...notebooks, initialFile];
      }
    } else if (isPythonItem(initialFile)) {
      if (
        !pythonScripts.some((f: FileItem) => samePath(f.path, initialFile.path))
      ) {
        pythonScripts = [...pythonScripts, initialFile];
      }
    } else if (isDatasetItem(initialFile)) {
      if (!datasets.some((f: FileItem) => samePath(f.path, initialFile.path))) {
        datasets = [...datasets, initialFile];
      }
    }
  }

  const renderFileItem = (file: FileItem) => (
    <div
      key={file.path}
      className={`group flex items-center space-x-3 p-2 cursor-pointer rounded-md hover:bg-gray-200 ${selectedFilePath === file.path ? "bg-blue-100" : ""
        }`}
      onClick={() => onFileSelect({ path: file.path, type: file.type })}
    >
      {getFileIcon(file)}
      <div className="flex-1 min-w-0">
        <p className="text-sm text-gray-800 truncate">{file.name}</p>
        <p className="text-xs text-gray-500">
          {formatFileSize(file.size)} • {formatTimeAgo(file.last_modified)}
        </p>
      </div>
      <button
        onClick={(e) => handleCopyPath(e, file.path)}
        className="p-1 rounded opacity-0 group-hover:opacity-100 hover:bg-gray-300 transition-opacity"
        title="Copy path"
      >
        <Copy size={14} className="text-gray-600" />
      </button>
      <button
        onClick={(e) => {
          e.stopPropagation();
          onFileDelete(file.path);
        }}
        className="p-1 rounded opacity-0 group-hover:opacity-100 hover:bg-red-300 transition-opacity"
        title="Delete file"
        disabled={isDeleting}
      >
        {isDeleting ? (
          <div className="h-3.5 w-3.5 animate-spin rounded-full border-2 border-gray-300 border-t-red-600"></div>
        ) : (
          <Trash2 size={14} className="text-red-600" />
        )}
      </button>
    </div>
  );

  const renderSection = (
    title: string,
    sectionKey: keyof typeof expandedSections,
    fileList: FileItem[]
  ) => (
    <div>
      <button
        onClick={() => toggleSection(sectionKey)}
        className="w-full flex items-center justify-between text-sm font-medium text-gray-700 hover:bg-gray-200 p-2 rounded-md"
      >
        <div className="flex items-center space-x-2">
          {expandedSections[sectionKey] ? (
            <ChevronDown size={16} />
          ) : (
            <ChevronRight size={16} />
          )}
          <span>{title}</span>
        </div>
        <span className="text-xs bg-gray-200 text-gray-600 rounded-full px-2">
          {fileList.length}
        </span>
      </button>
      {expandedSections[sectionKey] && (
        <div className="pl-6 mt-1 space-y-1">
          {fileList.length > 0 ? (
            fileList.map(renderFileItem)
          ) : (
            <p className="text-xs text-gray-400 pl-2">No files found.</p>
          )}
        </div>
      )}
    </div>
  );

  return (
    <aside className="min-h-11/12 flex flex-col border-r border-gray-200 w-80 p-3 space-y-4 flex-shrink-0">
      <h2 className="text-lg font-semibold text-gray-900 px-1">
        Project Explorer
      </h2>
      <div className="relative">
        <Search
          size={16}
          className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
        />
        <input
          type="text"
          placeholder="Search dataset, notebooks, etc..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full pl-9 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm"
        />
      </div>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            className="w-full justify-center"
            disabled={isCreatingFile}
          >
            {isCreatingFile ? (
              <>
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-gray-600"></div>
                Creating...
              </>
            ) : (
              <>
                <Plus size={16} className="mr-2" />
                New
              </>
            )}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-[--radix-dropdown-menu-trigger-width]">
          <DropdownMenuItem onClick={() => handleCreateNewFile("notebook")}>
            <FileText className="mr-2 h-4 w-4" />
            <span>Notebook</span>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => handleCreateNewFile("python")}>
            <FileCode className="mr-2 h-4 w-4" />
            <span>Python Script</span>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => handleCreateNewFile("markdown")}>
            <FileText className="mr-2 h-4 w-4" />
            <span>Markdown File</span>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => handleCreateNewFile("text")}>
            <File className="mr-2 h-4 w-4" />
            <span>Text File</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
      <div className="flex-1 overflow-y-auto custom-scroll space-y-2">
        {error ? (
          <p className="text-red-500">{error}</p>
        ) : (
          <>
            {renderSection("Notebooks", "notebooks", notebooks)}
            {renderSection("Datasets", "datasets", datasets)}
            {renderSection("Python Scripts", "pythonScripts", pythonScripts)}
          </>
        )}
      </div>
    </aside>
  );
};

const EditableFileViewer: React.FC<any> = ({
  file,
  content,
  onContentChange,
  onRename,
  isRenaming: isRenamingProp,
}) => {
  const [isRenaming, setIsRenaming] = useState(false);
  const [fileName, setFileName] = useState(file.name);
  const [optimisticName, setOptimisticName] = useState(file.name);

  useEffect(() => {
    setFileName(file.name);
    setOptimisticName(file.name);
  }, [file.name]);

  const handleRename = async () => {
    if (fileName.trim() && fileName !== file.name) {
      setOptimisticName(fileName);
      setIsRenaming(false);
      try {
        await onRename(fileName);
      } catch (error) {
        setOptimisticName(file.name);
        setFileName(file.name);
      }
    } else {
      setIsRenaming(false);
    }
  };

  return (
    <div className="flex flex-col h-full overflow-auto-y">
      <div className="flex-shrink-0 flex items-center p-2 border-b bg-gray-50">
        {isRenaming ? (
          <div className="flex items-center">
            <input
              type="text"
              value={fileName}
              onChange={(e) => setFileName(e.target.value)}
              onBlur={handleRename}
              onKeyDown={(e) => e.key === "Enter" && handleRename()}
              className="font-medium text-gray-800 ml-2 border-b-1 focus:outline-none"
              autoFocus
              disabled={isRenamingProp}
            />
            {isRenamingProp && (
              <div className="ml-2 h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-blue-600"></div>
            )}
          </div>
        ) : (
          <>
            <h3 className="font-medium text-gray-800 ml-2">{optimisticName}</h3>
            <Button
              variant="destructive"
              size="sm"
              onClick={() => setIsRenaming(true)}
            >
              <Edit className="text-gray-800" size={14} />
            </Button>
          </>
        )}
      </div>
      <textarea
        value={content}
        onChange={(e) => onContentChange(e.target.value)}
        className="w-full h-full p-4 font-mono text-sm border-none focus:outline-none resize-none bg-white"
        autoFocus
      />
    </div>
  );
};

const CSVEditor: React.FC<any> = ({
  file,
  content,
  onContentChange,
  onRename,
  isRenaming: isRenamingProp,
}) => {
  const [data, setData] = useState<string[][]>([]);
  const [headers, setHeaders] = useState<string[]>([]);
  const [editingCell, setEditingCell] = useState<{
    row: number;
    col: number;
  } | null>(null);
  const [editValue, setEditValue] = useState("");
  const [isRenaming, setIsRenaming] = useState(false);
  const [fileName, setFileName] = useState(file.name);
  const [optimisticName, setOptimisticName] = useState(file.name);

  useEffect(() => {
    setFileName(file.name);
    setOptimisticName(file.name);
  }, [file.name]);

  useEffect(() => {
    if (content) {
      const lines = content?.trim()?.split("\n");
      const headerRow = lines[0] ? lines[0]?.split(",") : [];
      const dataRows = lines.slice(1).map((line: string) => line?.split(","));
      setHeaders(headerRow);
      setData(dataRows);
    } else {
      setHeaders([]);
      setData([]);
    }
  }, [content]);

  const updateParentContent = (
    currentHeaders: string[],
    currentData: string[][]
  ) => {
    const newContent = [
      currentHeaders.join(","),
      ...currentData.map((row) => row.join(",")),
    ].join("\n");
    onContentChange(newContent);
  };

  const handleCellEdit = (rowIndex: number, colIndex: number) => {
    setEditingCell({ row: rowIndex, col: colIndex });
    setEditValue(data[rowIndex]?.[colIndex] || "");
  };

  const handleCellSave = () => {
    if (editingCell && data[editingCell.row]) {
      const newData: any = [...data];
      newData[editingCell.row][editingCell.col] = editValue;
      setData(newData);
      updateParentContent(headers, newData);
      setEditingCell(null);
      setEditValue("");
      toast.success("Cell updated successfully");
    }
  };

  const handleCellCancel = () => {
    setEditingCell(null);
    setEditValue("");
  };

  const addRow = () => {
    const newRow = headers.map(() => "");
    const newData = [...data, newRow];
    setData(newData);
    updateParentContent(headers, newData);
    toast.success("Row added successfully");
  };

  const deleteRow = (index: number) => {
    const newData = data.filter((_, i) => i !== index);
    setData(newData);
    updateParentContent(headers, newData);
    toast.success("Row deleted successfully");
  };

  const handleRename = async () => {
    if (fileName.trim() && fileName !== file.name) {
      setOptimisticName(fileName);
      setIsRenaming(false);
      try {
        await onRename(fileName);
      } catch (error) {
        setOptimisticName(file.name);
        setFileName(file.name);
      }
    } else {
      setIsRenaming(false);
    }
  };

  const handleDownload = () => {
    const blob = new Blob([content], { type: "text/csv;charset=utf-8;" });
    const link = document.createElement("a");
    const url = URL.createObjectURL(blob);
    link.setAttribute("href", url);
    link.setAttribute("download", file.name);
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className="h-full flex flex-col bg-white">
      <div className="p-4 border-b flex-shrink-0 flex items-center justify-between">
        <div className="flex items-center">
          {isRenaming ? (
            <div className="flex items-center">
              <input
                type="text"
                value={fileName}
                onChange={(e) => setFileName(e.target.value)}
                onBlur={handleRename}
                onKeyDown={(e) => e.key === "Enter" && handleRename()}
                className="font-medium text-gray-800 ml-2 border-b-1 focus:outline-none"
                autoFocus
                disabled={isRenamingProp}
              />
              {isRenamingProp && (
                <div className="ml-2 h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-blue-600"></div>
              )}
            </div>
          ) : (
            <>
              <h3 className="font-medium text-gray-800 ml-2">
                {optimisticName}
              </h3>
              <Button
                variant="destructive"
                size="sm"
                onClick={() => setIsRenaming(true)}
              >
                <Edit className="text-gray-800" size={14} />
              </Button>
            </>
          )}
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" onClick={addRow}>
            <Plus className="h-4 w-4 mr-2" />
            Add Row
          </Button>
          <Button variant="outline" size="sm" onClick={handleDownload}>
            <Download className="h-4 w-4 mr-2" />
            Download
          </Button>
        </div>
      </div>
      <div className="flex-1 overflow-auto p-4">
        <div className="border rounded-lg overflow-hidden">
          <table className="w-full text-sm">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-4 py-2 text-left font-medium text-gray-900 border-r">
                  #
                </th>
                {headers.map((header, index) => (
                  <th
                    key={index}
                    className="px-4 py-2 text-left font-medium text-gray-900 border-r last:border-r-0"
                  >
                    {header}
                  </th>
                ))}
                <th className="px-4 py-2 text-left font-medium text-gray-900">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody>
              {data.map((row, rowIndex) => (
                <tr key={rowIndex} className="border-t hover:bg-gray-50">
                  <td className="px-4 py-2 border-r text-gray-500">
                    {rowIndex + 1}
                  </td>
                  {row.map((cell, cellIndex) => (
                    <td
                      key={cellIndex}
                      className="px-4 py-2 border-r last:border-r-0 text-gray-700 cursor-pointer hover:bg-blue-50"
                      onClick={() => handleCellEdit(rowIndex, cellIndex)}
                    >
                      {editingCell?.row === rowIndex &&
                        editingCell?.col === cellIndex ? (
                        <Input
                          value={editValue}
                          onChange={(e) => setEditValue(e.target.value)}
                          onKeyDown={(e) => {
                            if (e.key === "Enter") handleCellSave();
                            else if (e.key === "Escape") handleCellCancel();
                          }}
                          onBlur={handleCellSave}
                          className="h-6 text-xs w-full"
                          autoFocus
                        />
                      ) : (
                        <span>{cell || ""}</span>
                      )}
                    </td>
                  ))}
                  <td className="px-4 py-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => deleteRow(rowIndex)}
                      className="h-6 w-6 p-0 text-red-500"
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

const ViewOnlyViewer: React.FC<any> = ({
  file,
  onRename,
  isRenaming: isRenamingProp,
}) => {
  const [isRenaming, setIsRenaming] = useState(false);
  const [fileName, setFileName] = useState(file.name);
  const [optimisticName, setOptimisticName] = useState(file.name);

  useEffect(() => {
    setFileName(file.name);
    setOptimisticName(file.name);
  }, [file.name]);

  const handleRename = async () => {
    if (fileName.trim() && fileName !== file.name) {
      setOptimisticName(fileName);
      setIsRenaming(false);
      try {
        await onRename(fileName);
      } catch (error) {
        setOptimisticName(file.name);
        setFileName(file.name);
      }
    } else {
      setIsRenaming(false);
    }
  };

  const ImageViewer = ({ file }: { file: FileItem }) => {
    const imageUrl = `data:${file.mimetype};base64,${file.content}`;
    return (
      <div className="p-4 flex items-center justify-center bg-white h-full">
        <img
          src={imageUrl}
          alt={file.name}
          className="max-w-full max-h-full object-contain"
        />
      </div>
    );
  };

  const fileHeader = (
    <div className="flex-shrink-0 flex items-center p-2 border-b bg-gray-50">
      {isRenaming ? (
        <div className="flex items-center">
          <input
            type="text"
            value={fileName}
            onChange={(e) => setFileName(e.target.value)}
            onBlur={handleRename}
            onKeyDown={(e) => e.key === "Enter" && handleRename()}
            className="font-medium text-gray-800 ml-2 border-b-1 focus:outline-none"
            autoFocus
            disabled={isRenamingProp}
          />
          {isRenamingProp && (
            <div className="ml-2 h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-blue-600"></div>
          )}
        </div>
      ) : (
        <>
          <h3 className="font-medium text-gray-800 ml-2">{optimisticName}</h3>
          <Button
            variant="destructive"
            size="sm"
            onClick={() => setIsRenaming(true)}
          >
            <Edit className="text-gray-800" size={14} />
          </Button>
        </>
      )}
    </div>
  );

  const extension = file?.name?.split(".").pop()?.toLowerCase();
  if (["png", "jpg", "jpeg", "gif", "svg"].includes(extension || "")) {
    if (typeof file.content === "string") {
      return (
        <div className="flex flex-col h-full">
          {fileHeader}
          <ImageViewer file={file} />
        </div>
      );
    }
  }

  return (
    <div className="flex flex-col h-full">
      {fileHeader}
      <div className="flex items-center justify-center h-full text-center text-gray-600">
        <div>
          <h3 className="text-lg font-semibold">Preview Not Available</h3>
          <p className="mt-1">Viewing for '{file.name}' is not supported.</p>
        </div>
      </div>
    </div>
  );
};

const NotebookContent: React.FC<any> = ({
  notebook: initialNotebook,
  onRename,
  cells,
  onCellsChange,
  workspace,
  isWorkspaceReady,
  updateActivity,
  executeCellMutation,
  executeNotebookMutation,
  isRenaming: isRenamingProp,
}) => {
  const [notebookName, setNotebookName] = useState(initialNotebook.name);
  const [optimisticName, setOptimisticName] = useState(initialNotebook.name);
  const [kernelStatus, setKernelStatus] = useState<
    "idle" | "busy" | "starting"
  >("idle");
  const [error, setError] = useState<string | null>(null);
  const [activeCell, setActiveCell] = useState<string | null>(null);
  const [isRenaming, setIsRenaming] = useState(false);
  const [executionStatuses, setExecutionStatuses] = useState<{
    [cellId: string]: "Executing" | "Executed";
  }>({});

  const interruptKernelMutation = useInterruptKernelMutation();
  const restartKernelMutation = useRestartKernelMutation();

  useEffect(() => {
    setNotebookName(initialNotebook.name);
    setOptimisticName(initialNotebook.name);
  }, [initialNotebook.name]);

  useEffect(() => {
    if (cells.length > 0 && !cells.find((c: Cell) => c.id === activeCell)) {
      setActiveCell(cells[0]?.id ?? null);
    }
  }, [cells, activeCell]);

  const handleRename = async () => {
    if (notebookName.trim() && notebookName !== initialNotebook.name) {
      setOptimisticName(notebookName);
      setIsRenaming(false);
      try {
        await onRename(notebookName);
      } catch (error) {
        setOptimisticName(initialNotebook.name);
        setNotebookName(initialNotebook.name);
      }
    } else {
      setIsRenaming(false);
    }
  };

  const addCell = (type: "code" | "markdown", afterId: string | null) => {
    const newCell: Cell = {
      id: `cell_${Date.now()}`,
      type,
      content: "",
      output: [],
      execution_count: null,
      metadata: {},
    };
    let newCells: Cell[];
    if (afterId) {
      const index = cells.findIndex((cell: Cell) => cell.id === afterId);
      newCells =
        index > -1
          ? [...cells.slice(0, index + 1), newCell, ...cells.slice(index + 1)]
          : [...cells, newCell];
    } else {
      newCells = [...cells, newCell];
    }
    onCellsChange(newCells);
    setActiveCell(newCell.id);
  };

  const deleteCell = (cellId: string) => {
    onCellsChange(cells.filter((cell: Cell) => cell.id !== cellId));
  };

  const updateCell = (cellId: string, content: string) => {
    onCellsChange(
      cells.map((cell: Cell) =>
        cell.id === cellId ? { ...cell, content } : cell
      )
    );
  };

  const executeCell = async (cellId: string) => {
    const cell = cells.find((c: Cell) => c.id === cellId);
    if (!cell || cell.type !== "code") {
      setError("Cannot execute: Cell not found or not a code cell.");
      return;
    }

    setExecutionStatuses((prev) => ({ ...prev, [cellId]: "Executing" }));
    const kernelId = workspace.kernelId;

    if (!kernelId || !isWorkspaceReady()) {
      setError("Not connected to JupyterLab. Please check your connection.");
      return;
    }

    setKernelStatus("busy");
    setError(null);
    updateActivity();

    onCellsChange((currentCells: Cell[]) =>
      currentCells.map((c) =>
        c.id === cellId
          ? { ...c, output: [], execution_count: (c.execution_count || 0) + 1 }
          : c
      )
    );

    try {
      const response = await executeCellMutation.mutateAsync({
        kernelId,
        request: { code: cell.content, timeout: 30000 },
      });

      onCellsChange((currentCells: Cell[]) =>
        currentCells.map((c) =>
          c.id === cellId
            ? {
              ...c,
              output: response.data.outputs || [],
              execution_count: response.data.execution_count,
            }
            : c
        )
      );
      setKernelStatus("idle");
    } catch (error: any) {
      setError(`Failed to execute cell: ${error.message || "Unknown error"}`);
      setKernelStatus("idle");
      onCellsChange((currentCells: Cell[]) =>
        currentCells.map((c) =>
          c.id === cellId
            ? {
              ...c,
              output: [
                {
                  output_type: "error",
                  ename: "ExecutionError",
                  evalue: error.message || "Unknown error",
                  traceback: [],
                },
              ],
            }
            : c
        )
      );
    } finally {
      setExecutionStatuses((prev) => ({ ...prev, [cellId]: "Executed" }));
    }
  };

  const executeAllCells = async () => {
    const kernelId = workspace.kernelId;
    if (!kernelId || !isWorkspaceReady()) {
      setError("Not connected to JupyterLab. Please check your connection.");
      return;
    }
    const codeCells = cells.filter((cell: Cell) => cell.type === "code");
    setExecutionStatuses((prev) => {
      const newStatuses = { ...prev };
      codeCells.forEach((cell) => {
        newStatuses[cell.id] = "Executing";
      });
      return newStatuses;
    });

    try {
      setKernelStatus("busy");
      setError(null);
      updateActivity();
      const response = await executeNotebookMutation.mutateAsync({
        kernelId,
        request: {
          notebook: {
            cells: codeCells.map((cell) => ({
              cell_type: cell.type,
              source: Array.isArray(cell.content)
                ? cell.content
                : [cell.content],
            })),
          },
          timeout: 60000,
          stopOnError: true,
        },
      });

      if (response.isSuccess && response.data) {
        onCellsChange((currentCells: Cell[]) =>
          currentCells.map((cell) => {
            const result = response.data.results.find(
              (r: any) =>
                r.cell_index === codeCells.findIndex((c) => c.id === cell.id)
            );
            if (result && cell.type === "code") {
              return {
                ...cell,
                output: result.outputs || [],
                execution_count: result.execution_count,
              };
            }
            return cell;
          })
        );
      }
      setKernelStatus("idle");
    } catch (error: any) {
      setError(
        `Failed to execute notebook: ${error.message || "Unknown error"}`
      );
      setKernelStatus("idle");
    } finally {
      setExecutionStatuses((prev) => {
        const newStatuses = { ...prev };
        codeCells.forEach((cell) => {
          newStatuses[cell.id] = "Executed";
        });
        return newStatuses;
      });
    }
  };

  const interruptKernel = async () => {
    const kernelId = workspace.kernelId;
    if (!kernelId) return;
    try {
      await interruptKernelMutation.mutateAsync(kernelId);
      setKernelStatus("idle");
    } catch (err) {
      console.error("Error interrupting kernel:", err);
    }
  };

  const restartKernel = async () => {
    const kernelId = workspace.kernelId;
    if (!kernelId) return;
    try {
      setKernelStatus("starting");
      await restartKernelMutation.mutateAsync(kernelId);
      setKernelStatus("idle");
      onCellsChange(
        cells.map((cell: Cell) => ({
          ...cell,
          execution_count: null,
          output: [],
        }))
      );
    } catch (err) {
      console.error("Error restarting kernel:", err);
      setKernelStatus("idle");
    }
  };

  const renderCellOutput = (output: any[]) => {
    return output.map((out, index) => {
      if (
        out.output_type === "execute_result" ||
        out.output_type === "display_data"
      ) {
        if (out.data["image/png"]) {
          return (
            <img
              key={index}
              src={`data:image/png;base64,${out.data["image/png"]}`}
              alt="cell output"
            />
          );
        }
        if (out.data["text/plain"]) {
          return (
            <div
              key={index}
              className="bg-gray-50 p-3 rounded-md font-mono text-sm"
            >
              <div className="text-gray-500 mb-1">
                Out[{out.execution_count}]:
              </div>
              <pre className="whitespace-pre-wrap">
                {out.data["text/plain"]}
              </pre>
            </div>
          );
        }
        return null;
      } else if (out.output_type === "stream") {
        return (
          <div
            key={index}
            className="bg-gray-50 p-3 rounded-md font-mono text-sm"
          >
            <div className="text-gray-500 mb-1">output</div>
            <pre className="whitespace-pre-wrap">{out.text}</pre>
          </div>
        );
      } else if (out.output_type === "error") {
        return (
          <div
            key={index}
            className="bg-red-50 p-3 rounded-md font-mono text-sm"
          >
            <div className="text-red-500 mb-1 font-bold">{out.ename}</div>
            <div className="text-red-700 whitespace-pre-wrap">{out.evalue}</div>
            {out.traceback && (
              <pre className="text-xs mt-2">{out.traceback.join("\n")}</pre>
            )}
          </div>
        );
      }
      return null;
    });
  };

  return (
    <div className="flex-1 flex flex-col overflow-hidden px-4 bg-white space-y-4">
      <div className="pt-2 space-y-4 border-b border-gray-200 flex-shrink-0">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            {isRenaming ? (
              <div className="flex items-center">
                <input
                  type="text"
                  value={notebookName}
                  onChange={(e) => setNotebookName(e.target.value)}
                  onBlur={handleRename}
                  onKeyDown={(e) => e.key === "Enter" && handleRename()}
                  className="font-medium text-gray-800 ml-2 border-b-1 focus:outline-none"
                  autoFocus
                  disabled={isRenamingProp}
                />
                {isRenamingProp && (
                  <div className="h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-blue-600"></div>
                )}
              </div>
            ) : (
              <>
                <h3 className="font-medium text-gray-800">{optimisticName}</h3>
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={() => setIsRenaming(true)}
                >
                  <Edit className="text-gray-800" size={14} />
                </Button>
              </>
            )}
            <Badge
              variant="secondary"
              className="bg-green-100 text-green-800 ml-2"
            >
              Python 3 (idle)
            </Badge>
          </div>
        </div>
        <div className="flex items-center gap-2 border-b border-gray-200 pb-3">
          <Button
            size="sm"
            variant="outline"
            onClick={() => addCell("code", activeCell)}
          >
            <Plus className="mr-2 h-4 w-4" /> Code
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={() => addCell("markdown", activeCell)}
          >
            <Plus className="mr-2 h-4 w-4" /> Markdown
          </Button>
          <div className="w-px bg-gray-200 h-6 mx-2"></div>
          <Button size="sm" variant="outline" onClick={executeAllCells}>
            <Play className="mr-2 h-4 w-4" /> Run All
          </Button>
          <Button size="sm" variant="outline" onClick={interruptKernel}>
            <Square className="mr-2 h-4 w-4" /> Stop
          </Button>
          <Button size="sm" variant="outline" onClick={restartKernel}>
            <RotateCcw className="mr-2 h-4 w-4" /> Restart
          </Button>
        </div>
      </div>
      <div className="flex-1 overflow-y-auto px-4 py-4 space-y-4">
        {cells.map((cell: Cell) => (
          <div key={cell.id} className="border border-gray-200 rounded-md">
            <div className="flex items-center justify-between bg-gray-50 px-3 py-1 border-b border-gray-200 text-xs text-gray-500">
              <div className="flex items-center gap-2">
                <span>
                  {cell.type === "code"
                    ? `Code[${executionStatuses[cell.id] === "Executing"
                      ? "*"
                      : cell.execution_count ?? " "
                    }]`
                    : "Markdown"}
                </span>
                {(() => {
                  if (cell.execution_count) {
                    const status = executionStatuses[cell.id];
                    if (status === "Executing") {
                      return (
                        <Badge
                          variant="secondary"
                          className="bg-blue-100 text-blue-600 font-normal pl-0 flex items-center"
                        >
                          <Loader2 className="h-4 w-4 animate-spin" />
                          Executing...
                        </Badge>
                      );
                    }
                    if (status === "Executed") {
                      return (
                        <Badge
                          variant="secondary"
                          className="bg-green-100 text-bits-green font-normal pl-0"
                        >
                          <Check className="h-4 w-4" />
                          Executed
                        </Badge>
                      );
                    }
                  }
                  return null;
                })()}
              </div>
              <div className="flex items-center">
                {cell.type === "code" && (
                  <button
                    className="p-1 hover:bg-gray-200 rounded"
                    onClick={() => executeCell(cell.id)}
                    disabled={kernelStatus === "busy"}
                  >
                    <Play size={14} />
                  </button>
                )}
                <button
                  className="p-1 hover:bg-gray-200 rounded"
                  onClick={() => addCell("code", cell.id)}
                >
                  <Plus size={14} />
                </button>
                <button
                  className="p-1 hover:bg-gray-200 rounded text-red-500 hover:text-red-700"
                  onClick={() => deleteCell(cell.id)}
                >
                  <Trash2 size={14} />
                </button>
              </div>
            </div>
            <div className="p-3">
              <textarea
                value={cell.content}
                onChange={(e) => updateCell(cell.id, e.target.value)}
                className="w-full font-mono text-sm border-none focus:outline-none focus:ring-0 resize-none bg-transparent"
                rows={(cell.content || "")?.split("\n").length}
              />
              {cell.output && cell.output.length > 0 && (
                <div className="mt-3 border-t border-gray-200 pt-3 space-y-2">
                  {renderCellOutput(cell.output)}
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// --- NEW WORKSPACE COMPONENT ---
const Workspace = forwardRef(
  (
    {

      initialFile,
      onStateChange,
      onLastSavedChange,
      onAuthFailed,
    }: any,
    ref
  ) => {
    const { workspace, updateWorkspace, isWorkspaceReady, updateActivity } =
      useJupyterWorkspace();
    const queryClient = useQueryClient();
    const updateContentMutation = useUpdateContentMutation();
    const executeCellMutation = useExecuteCellMutation();
    const executeNotebookMutation = useExecuteNotebookMutation();
    const renameContentMutation = useRenameContentMutation();
    const deleteContentMutation = useDeleteContentMutation();
    const { projectId } = useParams()
    console.log(initialFile, onStateChange, onLastSavedChange, onAuthFailed)
    const [selectedFile, setSelectedFile] = useState<FileItem | null>(null);
    const [editableFileContent, setEditableFileContent] = useState<string>("");
    const [notebookCells, setNotebookCells] = useState<Cell[]>([]);
    const [isDirty, setIsDirty] = useState(false);
    const [isSaving, setIsSaving] = useState(false);
    const [isRenaming, setIsRenaming] = useState(false);
    const [isDeleting, setIsDeleting] = useState(false);
    const [isFileContentLoading, setIsFileContentLoading] = useState(false);
    const {
      data: filesData,
      isLoading: isLoadingFiles,
      isError: isFilesError,
      error: filesError,
    } = useQuery({
      queryKey: ["files", projectId],
      queryFn: async () => {
        if (!projectId) {
          throw new Error("projectId is missing");
        }
        const res = await getContents(projectId);
        return res.content || [];
      },
      enabled: Boolean(projectId),
      staleTime: 1000 * 60 * 5, // 5 minutes
      gcTime: 1000 * 60 * 30, // 30 minutes
      refetchOnWindowFocus: false,
    });

    const files = filesData ?? [];



    useEffect(() => {
      onStateChange({ isDirty, isSaving });
    }, [isDirty, isSaving, onStateChange]);

    const handleFileSelect = useCallback(
      async (fileInfo: { path: string; type: string }) => {
        if (fileInfo.path === selectedFile?.path) return;

        if (fileInfo.type === "directory") {
          setSelectedFile(null);
          return;
        }

        setIsFileContentLoading(true);
        try {
          const fileDetails = await getContents(fileInfo.path);
          setSelectedFile(fileDetails as FileItem);
          setEditableFileContent("");
          setNotebookCells([]);
          setIsDirty(false);

          if (fileDetails.type === "notebook") {
            const notebookContent = fileDetails.content;
            const cells = notebookContent?.cells || [];
            const transformedCells = cells.map((apiCell: any) => ({
              id:
                apiCell.id ||
                `cell_${Date.now()}_${Math.random()
                  .toString(36)
                  .substring(2, 9)}`,
              type: apiCell.cell_type,
              content: Array.isArray(apiCell.source)
                ? apiCell.source.join("\n")
                : apiCell.source || "",
              output: apiCell.outputs || [],
              execution_count: apiCell.execution_count ?? null,
              metadata: apiCell.metadata || {},
            }));
            if (transformedCells.length === 0) {
              transformedCells.push({
                id: `cell_${Date.now()}`,
                type: "code",
                content: "",
                output: [],
                execution_count: null,
                metadata: {},
              });
            }
            setNotebookCells(transformedCells);
          } else if (fileDetails.type === "file") {
            setEditableFileContent(fileDetails.content || "");
          }
        } catch (err: any) {
          toast.error("Could not load the selected file.");
        } finally {
          setIsFileContentLoading(false);
        }
      },
      [selectedFile?.path]
    );

    useEffect(() => {
      if (!isLoadingFiles && files.length > 0 && !selectedFile) {
        const fileToSelect =
          initialFile &&
          files.find((f: FileItem) => f.path === initialFile.path);

        if (fileToSelect) {
          handleFileSelect(fileToSelect);
        } else {
          const sortedFiles = [...files].sort(
            (a, b) =>
              new Date(b.last_modified!).getTime() -
              new Date(a.last_modified!).getTime()
          );
          const mostRecentFile = sortedFiles[0];
          if (mostRecentFile && mostRecentFile.type !== "directory") {
            handleFileSelect({
              path: mostRecentFile.path,
              type: mostRecentFile.type,
            });
          }
        }
      }
    }, [files, isLoadingFiles, selectedFile, initialFile, handleFileSelect]);

    const handleContentChange = (newContent: string) => {
      setEditableFileContent(newContent);
      setIsDirty(true);
    };

    const handleNotebookCellsChange = (value: SetStateAction<Cell[]>) => {
      setNotebookCells(value);
      setIsDirty(true);
    };

    const handleFileCreate = async (
      fileType: "notebook" | "python" | "markdown" | "text",
      fileName: string
    ) => {
      try {
        const folderPath = workspace.folderPath || `project_${projectId}`;
        const filePath = `${folderPath}/${fileName}`;
        let content: any, type: string, format: string;

        if (fileType === "notebook") {
          type = "notebook";
          format = "json";
          content = {
            cells: [
              {
                cell_type: "code",
                execution_count: null,
                metadata: {},
                outputs: [],
                source: [""],
              },
            ],
            metadata: {
              kernelspec: {
                display_name: "Python 3",
                language: "python",
                name: "python3",
              },
              language_info: { name: "python", version: "3.9.7" },
            },
            nbformat: 4,
            nbformat_minor: 5,
          };
        } else {
          type = "file";
          format = "text";
          content =
            fileType === "python"
              ? "# Python script\n"
              : fileType === "markdown"
                ? "# Markdown File\n\nYour content here..."
                : "";
        }

        await updateContentMutation.mutateAsync({
          path: filePath,
          content,
          type,
          format,
        });
        queryClient.invalidateQueries({ queryKey: ["files", projectId] });
        await handleFileSelect({
          path: filePath,
          type: fileType === "notebook" ? "notebook" : "file",
        });
        updateActivity();
      } catch (err: any) {
        toast.error(
          `Failed to create ${fileType} file: ${err.message || "Unknown error"}`
        );
      }
    };

    const handleSaveNow = async () => {
      if (!selectedFile || !isDirty || !projectId) return;
      setIsSaving(true);
      try {
        if (selectedFile.type === "notebook") {
          const notebookContentToSave = {
            cells: notebookCells.map((cell) => ({
              cell_type: cell.type,
              metadata: cell.metadata || {},
              source: Array.isArray(cell.content)
                ? cell.content
                : [cell.content],
              ...(cell.type === "code" && {
                execution_count: cell.execution_count,
                outputs: cell.output || [],
              }),
            })),
            metadata: {
              kernelspec: {
                display_name: "Python 3",
                language: "python",
                name: "python3",
              },
              language_info: { name: "python", version: "3.9.7" },
              ...(selectedFile as any).content?.metadata,
            },
            nbformat: 4,
            nbformat_minor: 5,
          };
          await updateContentMutation.mutateAsync({
            path: selectedFile.path,
            content: notebookContentToSave,
            type: "notebook",
            format: "json",
          });
        } else if (selectedFile.type === "file") {
          await updateContentMutation.mutateAsync({
            path: selectedFile.path,
            content: editableFileContent,
            type: "file",
            format: "text",
          });
        }
        onLastSavedChange(new Date());
        updateActivity();
        setIsDirty(false);
        toast.success("File saved successfully!");
      } catch (err: any) {
        toast.error(`Failed to save file: ${err.message || "Unknown error"}`);
      } finally {
        setIsSaving(false);
      }
    };

    const handleUpload = () => {
      const input = document.createElement("input");
      input.type = "file";
      input.multiple = false;
      input.accept = ".ipynb,.py,.csv";
      input.onchange = async (e) => {
        const file = (e.target as HTMLInputElement).files?.[0];
        if (!file || !projectId) return;

        if (
          files.some(
            (existingFile: FileItem) => existingFile.name === file.name
          )
        ) {
          toast.error(
            `A file named "${file.name}" already exists. Please rename it.`
          );
          return;
        }

        const extension = file?.name?.split(".").pop()?.toLowerCase();
        if (!extension || !["ipynb", "py", "csv"].includes(extension)) {
          toast.error(
            "Invalid file type. Please upload a .ipynb, .py, or .csv file."
          );
          return;
        }

        const loadingToast = toast.loading(`Uploading "${file.name}"...`);
        try {
          const folderPath = workspace.folderPath || `project_${projectId}`;
          const filePath = `${folderPath}/${file.name}`;
          const reader = new FileReader();
          reader.onload = async (event) => {
            if (!event.target?.result) {
              toast.error("Failed to read file content.");
              toast.dismiss(loadingToast);
              return;
            }
            let content: any, type: string, format: string;
            const fileContent = event.target.result as string;
            if (extension === "ipynb") {
              try {
                content = JSON.parse(fileContent);
                type = "notebook";
                format = "json";
              } catch (parseError) {
                toast.error("Invalid notebook file.");
                toast.dismiss(loadingToast);
                return;
              }
            } else {
              content = fileContent;
              type = "file";
              format = "text";
            }
            await updateContentMutation.mutateAsync({
              path: filePath,
              content,
              type,
              format,
            });
            toast.dismiss(loadingToast);
            toast.success(`File "${file.name}" uploaded successfully!`);
            updateActivity();
            queryClient.invalidateQueries({ queryKey: ["files", projectId] });
          };
          reader.onerror = () => {
            toast.dismiss(loadingToast);
            toast.error(`Error reading file "${file.name}".`);
          };
          reader.readAsText(file);
        } catch (err: any) {
          toast.dismiss(loadingToast);
          toast.error(
            `Upload failed: ${err?.message || "An unknown error occurred."}`
          );
        }
      };
      input.click();
    };

    const handleRename = async (newName: string) => {
      if (
        !selectedFile ||
        !newName ||
        newName === selectedFile.name ||
        isRenaming
      )
        return;
      setIsRenaming(true);
      const loadingToast = toast.loading(`Renaming to "${newName}"...`);
      try {
        const oldPath = selectedFile.path;
        const pathParts = oldPath?.split("/");
        pathParts[pathParts.length - 1] = newName;
        const newPath = pathParts.join("/");
        await renameContentMutation.mutateAsync({ oldPath, newPath });
        toast.dismiss(loadingToast);
        toast.success(`File renamed to "${newName}" successfully!`);
        updateActivity();
        queryClient.invalidateQueries({ queryKey: ["files", projectId] });
      } catch (err: any) {
        toast.dismiss(loadingToast);
        if (err?.response?.status === 409)
          toast.error(`A file named "${newName}" already exists.`);
        else if (err?.response?.status === 404) toast.error("File not found.");
        else
          toast.error(
            `Failed to rename file: ${err?.response?.data?.message || err?.message || "Unknown error"
            }`
          );
        throw err;
      } finally {
        setIsRenaming(false);
      }
    };

    const handleDelete = async (filePath: string) => {
      if (!filePath || isDeleting) return;

      const isConfirmed = window.confirm(
        `Are you sure you want to delete this file? This action cannot be undone.`
      );
      if (!isConfirmed) return;

      setIsDeleting(true);
      try {
        if (selectedFile?.path === filePath) {
          setSelectedFile(null);
        }

        await deleteContentMutation.mutateAsync(filePath);

        toast.success("File deleted successfully!");
        updateActivity();
        queryClient.invalidateQueries({ queryKey: ["files", projectId] });
      } catch (err: any) {
        toast.error(
          `Failed to delete file: ${err?.message || "Unknown error"}`
        );
      } finally {
        setIsDeleting(false);
      }
    };

    useImperativeHandle(ref, () => ({
      save: handleSaveNow,
      upload: handleUpload,
      getFiles: () => files,
    }));

    const isNotebook = (file: FileItem | null): file is NotebookFile => {
      if (!file?.name) return false;
      return (
        file.type === "notebook" ||
        file.name.endsWith(".ipynb") ||
        file.mimetype === "application/x-ipynb+json"
      );
    };

    if (isLoadingFiles) {
      return (
        <div className="flex flex-1 items-center justify-center">
          <Loader2 className="h-10 w-10 animate-spin text-gray-500" />
        </div>
      );
    }

    if (isFilesError) {
      onAuthFailed(true);
      return (
        <div className="flex flex-1 items-center justify-center text-red-500">
          <p>Error loading workspace: {(filesError as Error).message}</p>
        </div>
      );
    }

    const renderContent = () => {
      if (isFileContentLoading) {
        return (
          <div className="flex h-full items-center justify-center">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        );
      }

      if (!selectedFile) {
        if (files.length > 0) {
          return (
            <div className="flex items-center justify-center h-full text-gray-500">
              <p>Select a file to begin.</p>
            </div>
          );
        }
        return (
          <div className="flex items-center justify-center h-full text-gray-500">
            <p>No files in this workspace. Create a new file to start.</p>
          </div>
        );
      }

      if (isNotebook(selectedFile)) {
        return (
          <NotebookContent
            notebook={selectedFile as Notebook}
            onRename={handleRename}
            cells={notebookCells}
            onCellsChange={handleNotebookCellsChange}
            workspace={workspace}
            isWorkspaceReady={isWorkspaceReady}
            updateActivity={updateActivity}
            executeCellMutation={executeCellMutation}
            executeNotebookMutation={executeNotebookMutation}
            isRenaming={isRenaming}
          />
        );
      }
      if (isCsvFile(selectedFile)) {
        return (
          <CSVEditor
            file={selectedFile}
            content={editableFileContent}
            onContentChange={handleContentChange}
            onRename={handleRename}
            isRenaming={isRenaming}
          />
        );
      }
      if (isEditableFile(selectedFile)) {
        return (
          <EditableFileViewer
            file={selectedFile}
            content={editableFileContent}
            onContentChange={handleContentChange}
            onRename={handleRename}
            isRenaming={isRenaming}
          />
        );
      }
      return (
        <ViewOnlyViewer
          file={selectedFile}
          onRename={handleRename}
          isRenaming={isRenaming}
        />
      );
    };

    return (
      <div className="flex flex-1 overflow-hidden">
        <ProjectExplorer
          files={files}
          onFileSelect={handleFileSelect}
          onFileCreate={handleFileCreate}
          onFileDelete={handleDelete}
          selectedFilePath={selectedFile?.path || null}
          isDeleting={isDeleting}
          initialFile={initialFile}
        />
        <main className="flex-1 flex flex-col overflow-hidden">
          {renderContent()}
        </main>
      </div>
    );
  }
);

// --- MAIN PAGE COMPONENT ---
export default function NotebookPage() {
  const { goBack, pageParams, navigateTo } = useNavigation();
  const { workspace } = useJupyterWorkspace();

  const navigate = useNavigate()
  const location = useLocation();
   const {projectId:projectIdToCheck}= useParams()
  const params = new URLSearchParams(location.search);
  const sess = params.get("sess");
  
  const workspaceRef = useRef<any>(null);
  const [authFailed, setAuthFailed] = useState(false);
  const [toolbarState, setToolbarState] = useState({
    isDirty: false,
    isSaving: false,
  });
  const [lastSaved, setLastSaved] = useState(new Date(Date.now() - 30 * 1000));
  const projectId = projectIdToCheck;
  const handleWorkspaceStateChange = useCallback((newState: any) => {
    setToolbarState(newState);
  }, []);

  const handleSaveNow = () => workspaceRef.current?.save();
  const handleUpload = () => workspaceRef.current?.upload();

  const handleNavigate = (destination: string, files: File[]) => {
    const navigate = useNavigate();
    navigate(paths.notebooks, { state: { files } });
  };
  if (authFailed) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <h3 className="text-xl font-semibold text-red-500 mb-2">
            Connection Failed
          </h3>
          <p className="text-gray-600">
            Could not connect to the Jupyter server. Please ensure it is running
            and refresh.
          </p>
        </div>
      </div>
    );
  }
  const handleBack = (sess: any) => {
    navigate(paths.projectDetails.replace(":projectId", sess));
  }
 
  return (
    <div className="flex h-[102vh] overflow-hidden w-full bg-white flex-col">
      <div className="border-b bg-white flex-shrink-0">
        <div className="p-4 flex justify-between mt-4">
          <div className="flex">
            <button
              onClick={() => { handleBack(sess) }}
              className="p-2 rounded-full hover:bg-gray-50"
            >
              <ArrowLeft className="h-5 w-5 text-gray-700" />
            </button>
            <div className="ml-3">
              <h1 className="text-xl font-semibold text-gray-900">
                Jupyter Notebook Environment
              </h1>
              <p className="text-sm text-gray-600">
                Housing Price Prediction Assignment
              </p>
            </div>
          </div>
          <div className="text-sm text-gray-400">{workspace.instanceId}</div>
        </div>
      </div>

      <div className="border-b border-gray-200 bg-gray-50 px-4 py-3 flex items-center justify-between">
        <div>
          <Badge className=" text-gray-800">
            <CheckCircle className="text-bits-green h-4 w-4" />
            Last saved: {formatTimestamp(lastSaved)}
          </Badge>
          <Badge className=" text-gray-800">
            <Clock className="text-bits-blue h-4 w-4" />
            Last activity:{" "}
            {workspace.lastActivity
              ? formatTimestamp(new Date(workspace.lastActivity))
              : "just now"}
          </Badge>
        </div>
        <div className="flex items-center space-x-2 text-sm ">
          <Button
            variant="ghost"
            className="text-gray-800 bg-white"
            onClick={() => navigate(
              `${paths.datasets.replace(":projectId", projectId)}`
            )}
          >
            <Database className="h-4 w-4" /> Datasets
          </Button>
          <Button
            variant="ghost"
            className="text-gray-800 bg-white"
            onClick={() => navigate(
              `${paths.pythonScripts.replace(":projectId", projectId)}`
            )}
          >
            <FileCode className="h-4 w-4" /> PythonScripts
          </Button>
          <Button
            variant="ghost"
            className="text-gray-800 bg-white"
            onClick={() => navigate(
              `${paths.notebooks.replace(":projectId", projectId)}`
            )}
          >
            <BookOpen className="h-4 w-4" /> Notebooks
          </Button>
          <Button
            variant="ghost"
            className="text-gray-800 bg-white"
            onClick={handleSaveNow}
            disabled={!toolbarState.isDirty || toolbarState.isSaving}
          >
            {toolbarState.isSaving ? (
              <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-gray-600"></div>
            ) : (
              <Save className="h-4 w-4" />
            )}
            {toolbarState.isSaving ? "Saving..." : "Save Now"}
          </Button>
          <Button
            variant="ghost"
            className="text-gray-800 bg-white"
            onClick={handleUpload}
          >
            <Upload className="h-4 w-4" /> Upload
          </Button>
          <Button variant="ghost" className="text-gray-800 bg-white">
            <CheckCircle className=" h-4 w-4" /> Submit Assignment
          </Button>
        </div>
      </div>

      <Workspace
        ref={workspaceRef}
        projectId={projectId}
        initialFile={pageParams?.file}
        onStateChange={handleWorkspaceStateChange}
        onLastSavedChange={setLastSaved}
        onAuthFailed={setAuthFailed}
      />
    </div>
  );
}