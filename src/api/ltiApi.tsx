import axios, { AxiosInstance } from "axios";
import {
  useQuery,
  useMutation,
  UseQueryOptions,
  QueryKey,
} from "@tanstack/react-query";
import getBITSEndApiMap from "../apiMap";
import { tokenService } from "../services/TokenService";

// Auth setup similar to other API files
function getAuthToken() {
  const t = tokenService.getToken();
  return t ? `Bearer ${t}` : "";
}

function attachAuth(instance: AxiosInstance) {
  instance.interceptors.request.use((config) => {
    const auth = getAuthToken();
    if (auth) {
      config.headers = config.headers ?? {};
      config.headers["Authorization"] = auth;
    }
    return config;
  });
  return instance;
}

const api = attachAuth(
  axios.create({
    headers: { "Content-Type": "application/json" },
    withCredentials: true,
    timeout: 120000,
  })
);

// TypeScript Interfaces

export interface LTIPlatform {
  id: string;
  platformName: string;
  platformId?: string;
}

export interface LTIContext {
  id: string;
  platformId: string;
  deploymentId: string;
  courseId?: string | null;
  contextId: string;
  contextType: string;
  contextTitle: string;
  contextLabel: string;
  isActive: boolean;
  created_at: string;
  updated_at: string;
  deletedAt?: string | null;
  platform: LTIPlatform;
}

export interface LTIContextsResponse {
  isSuccess: boolean;
  message: string;
  data: {
    success: boolean;
    data: LTIContext[];
    totalPages: number;
    currentPage: number;
    totalItems: number;
  };
}

export interface LTIContextMember {
  id: string;
  name: string;
  email: string;
  lms_user_id: string;
  status: string;
  profile_picture?: string | null;
  roleId: string;
  roleName: string;
}

export interface LTIContextMembersResponse {
  isSuccess: boolean;
  message: string;
  data: {
    success: boolean;
    data: LTIContextMember[];
  };
}

export interface LTILineItem {
  id: string;
  scoreMaximum: string;
  label: string;
  tag?: string | null;
  startDateTime?: string | null;
  endDateTime?: string | null;
}

export interface LTIResourceLink {
  id: string;
  platformId: string;
  contextId: string;
  projectId?: string | null;
  resourceLinkId: string;
  resourceLinkTitle: string;
  resourceLinkDescription?: string | null;
  customParameters: Record<string, any>;
  isActive: boolean;
  created_at: string;
  updated_at: string;
  deletedAt?: string | null;
  context: {
    id: string;
    contextTitle: string;
    contextLabel: string;
  };
  platform: {
    id: string;
    platformName: string;
  };
  lineItem?: LTILineItem | null;
}

export interface LTIResourceLinksResponse {
  isSuccess: boolean;
  message: string;
  data: {
    success: boolean;
    data: LTIResourceLink[];
    totalPages: number;
    currentPage: number;
    totalItems: number;
  };
}

export interface LinkProjectRequest {
  resourceLinkId: string; // This is the ID of the resource link record
  projectId: string;
  total_points: number;
  instructor_ids: string[];
}

export interface LinkProjectResponse {
  success: boolean;
  message: string;
  data?: any;
}

export interface GetContextMembersParams {
  contextId: string;
  platformId?: string | undefined;
  role?: string;
}

export interface GetUnassignedResourceLinksParams {
  contextId: string;
  platformId?: string | undefined;
}

// API Functions

export async function getLTIContexts(): Promise<LTIContext[]> {
  const url = getBITSEndApiMap("ltiGetContexts");
  const { data } = await api.get<LTIContextsResponse>(url);
  return data.data.data;
}

export async function getLTIContextMembers(
  params: GetContextMembersParams
): Promise<LTIContextMember[]> {
  const url = getBITSEndApiMap("ltiGetContextMembers");
  const { data } = await api.get<LTIContextMembersResponse>(url, { params });
  return data.data.data;
}

export async function getUnassignedResourceLinks(
  params: GetUnassignedResourceLinksParams
): Promise<LTIResourceLink[]> {
  const url = getBITSEndApiMap("ltiGetUnassignedResourceLinks");
  const { data } = await api.get<LTIResourceLinksResponse>(url, { params });
  return data.data.data;
}

export async function linkProject(
  request: LinkProjectRequest
): Promise<LinkProjectResponse> {
  const url = getBITSEndApiMap("ltiLinkProject");
  const { data } = await api.post<LinkProjectResponse>(url, request);
  return data;
}

// React Query Hooks

export function useLTIContextsQuery(enabled = true) {
  return useQuery({
    queryKey: ["lti", "contexts"] as QueryKey,
    queryFn: getLTIContexts,
    enabled,
    staleTime: 60_000,
  } as UseQueryOptions<LTIContext[]> as any);
}

export function useLTIContextMembersQuery(
  params: GetContextMembersParams,
  enabled = true
) {
  return useQuery({
    queryKey: [
      "lti",
      "contextMembers",
      params.contextId,
      params.role,
    ] as QueryKey,
    queryFn: () => getLTIContextMembers(params),
    enabled: enabled && !!params.contextId,
    staleTime: 60_000,
  } as UseQueryOptions<LTIContextMember[]> as any);
}

export function useUnassignedResourceLinksQuery(
  params: GetUnassignedResourceLinksParams,
  enabled = true
) {
  return useQuery({
    queryKey: ["lti", "resourceLinks", params.contextId] as QueryKey,
    queryFn: () => getUnassignedResourceLinks(params),
    enabled: enabled && !!params.contextId,
    staleTime: 60_000,
  } as UseQueryOptions<LTIResourceLink[]> as any);
}

export function useLinkProjectMutation() {
  return useMutation({
    mutationFn: linkProject,
  });
}
