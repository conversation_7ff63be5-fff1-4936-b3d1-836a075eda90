import axios, { AxiosInstance } from "axios";
import {
  useQ<PERSON>y,
  UseQueryOptions,
  Query<PERSON>ey,
  useMutation,
  UseQueryResult,
} from "@tanstack/react-query";
import getBITSEndApiMap from "../apiMap";
import { tokenService } from "../services/TokenService";

// Helper function to replace path params like :projectId, :kernelId, etc.
function buildUrl(template: string, params: Record<string, string | number>) {
  return template.replace(/:([a-zA-Z0-9_]+)/g, (_, key) =>
    String(params[key] ?? ":" + key)
  );
}

function getAuthToken() {
  const t = tokenService.getToken();
  return t ? `Bearer ${t}` : "";
}

function attachAuth(instance: AxiosInstance) {
  instance.interceptors.request.use((config) => {
    const auth = getAuthToken();

    if (auth) {
      config.headers = config.headers ?? {};
      config.headers["Authorization"] = auth;
    }

    // Handle FormData by removing Content-Type header to let browser set it automatically
    if (config.data instanceof FormData) {
      delete config.headers["Content-Type"];
    }

    return config;
  });
  return instance;
}

const api = attachAuth(
  axios.create({
    headers: { "Content-Type": "application/json" },
    withCredentials: true,
    timeout: 120000,
  })
);

export type Difficulty = "beginner" | "intermediate" | "advanced";

export type ProjectType = "group" | "individual";

type dataset = {
  id: string;
  name: string;
  file: File;
  size: string;
};

export type checkpoints = {
  project_id: string;
  isScreen: number;
  title: string;
  description: string;
  checkpoint_number: number;

  start_date: string;
  due_date: string;
  weight_percentage: number;
  is_required: boolean;
  status: string;
  metadata: {};
};

export type GradingRubric = {
  project_id: string;
  title: string;
  description: string;
  checkpoint: string;
  criteria?: Array<{
    name: string;
    description: string;
    points: number;
  }>;

  total_points: number;
  grading_scale: {};
  is_template: boolean;
  template_name: string;
  checkpoint_mapping: {};
  metadata: {};
};

export interface ProjectData {
  // Step 1: Project Details
  id: string;
  title: string;
  teachingAssistants?:any[];
  // categoryId: string;
  courseId: string;
  description: string;
  instructorIds: string[];
  teachingAssId: string[];
  projectType: ProjectType;
  difficulty_level: Difficulty;
  totalPoints: number;
  estimatedHours: string;
  tags: string[];
  project_overview: string;
  learning_objectives: string;
  prerequisites: string;
  isScreen: number;
  sandBoxDuration: string;
  dueDate: string;
isStep?:number;
  // Step 2: Assets & Instructions
  projectId: string;
  instructions: string;

  datasets: dataset[];
  // Step 3: Review Checkpoints
  checkpoints: checkpoints[];

  // Step 4: Project Settings
  sandboxResourceProfile: string;
  maxSubmissions: number;
  acceptForDays: number;
  lateSubmissionsAllowed: boolean;
  gradingRubric: GradingRubric[];
}

export interface CreateProjectResponse {
  isSuccess: boolean;
  message: string;
  data: {
    id: string;
    projectId: string;
    title: string;
    status: string;
    courseId: string;
    totalPoints: number;
    createdAt: string;
    isScreen: number;
  };
}

export interface UploadProjectDatasetResponse {
  isSuccess: boolean;
  message: string;
  data: DatasetFile[];
}

export interface DatasetFile {
  url: string;
  key: string;
  bucket: string;
  size: number;
  contentType: string;
  etag: string;
  downloadUrl: DownloadUrl;
  isPublic: boolean;
  fileName: string;
}

export interface DownloadUrl {
  presignedUrl: string;
  key: string;
}

export interface RubricCriteria {
  name: string;
  points: number;
  description: string;
}

export interface ProjectBrief {
  id: string;
  title: string;
  description: string;
  status: string;
}

// Main rubric item
export interface RubricItem {
  id: string;
  project_id: string;
  title: string;
  description: string;
  criteria: RubricCriteria[];
  total_points: string;
  grading_scale: {};
  is_template: boolean;
  template_name: string | null;
  created_by: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  project: ProjectBrief;
}

// Concrete response type for your JSON
export type CreateRubricResponse = {
  isSuccess: boolean;
  message: string;
  data: RubricItem[];
};

export interface CheckpointItem {
  id: string;
  metadata: {};
  project_id: string;
  title: string;
  description: string;
  checkpoint_number: number;
  due_date: string;
  weight_percentage: string;
  is_required: boolean;
  status: string;
  created_by: string;
  updatedAt: string; // ISO timestamp string
  createdAt: string; // ISO timestamp string
  deletedAt: string | null;
}

export interface CreateCheckPointResponse {
  isSuccess: boolean;
  message: string;
  data: CheckpointItem[];
}

export interface Course {
  id: string;
  name: string;
  code: string;
  term: string;
  academicYear: string;
  startDate: string;
  endDate: string;
}

export interface CourseResponse {
  isSuccess: boolean;
  message: string;
  data: Course[];
}

interface CourseTeachingStaffResponse {
  isSuccess: boolean;
  message: string;
  data: CourseStaff;
}

export interface CourseStaff {
  instructors: StaffMember[];
  tas: StaffMember[];
}

interface StaffMember {
  id: string;
  name: string;
  email: string;
  profilePicture: string;
  role: "instructor" | "ta";
  enrolledAt: string;
}

// Create Project
export async function createProject(
  projectData: ProjectData
): Promise<CreateProjectResponse> {
  const url = getBITSEndApiMap("createProject");
  const { data } = await api.post<CreateProjectResponse>(url, projectData);
  console.log("create project response", data);
  return data;
}
export async function editProject(
  projectId: string,
  projectData: ProjectData
): Promise<CreateProjectResponse> {
  const baseUrl = getBITSEndApiMap("editProject");
  console.log(baseUrl)
  const url = `${baseUrl}/${projectId}/enhanced`;

  const { data } = await api.put<CreateProjectResponse>(url, projectData);
  console.log("edit project response", data);
  return data;
}


//Upload dataset
export async function uploadDataset(
  fd: FormData
): Promise<UploadProjectDatasetResponse> {
  const url = getBITSEndApiMap("uploadDataset");
  const { data } = await api.post<UploadProjectDatasetResponse>(url, fd);
  console.log("upload dataset response", data);
  return data;
}

//Create Checkpoint
export async function createCheckPoint(
  checkPointsData: checkpoints[]
): Promise<CreateCheckPointResponse> {
  const url = getBITSEndApiMap("createCheckPoint");
  const { data } = await api.post<CreateCheckPointResponse>(
    url,
    checkPointsData
  );
  console.log("create checkpoint response", data);
  return data;
}

// ✅ React Query mutation hook
export function useUpdateCheckPointsMutation() {
  return useMutation({
    mutationFn: updateCheckPoints,
  });
}

// ✅ API call function
export async function updateCheckPoints(
  checkPointsData: checkpoints[]
): Promise<CreateCheckPointResponse> {
  // Assuming you have a variable for the bulk update path
  const bulkUpdateUrl = getBITSEndApiMap("bulkUpdateCheckPoints"); // {{base_host}}/api/checkpoints/bulk-update
  const { data } = await api.put<CreateCheckPointResponse>(bulkUpdateUrl, checkPointsData);
  console.log("bulk update checkpoint response", data);
  return data;
}


//Create rubric
export async function createRubric(
  rubricData: GradingRubric[]
): Promise<CreateRubricResponse> {
  const url = getBITSEndApiMap("createRubric");
  const { data } = await api.post<CreateRubricResponse>(url, rubricData);
  console.log("create rubric response", data);
  return data;
}

export async function getCourses(): Promise<Course[]> {
  const url = getBITSEndApiMap("getCourses");
  const { data } = await api.get<CourseResponse>(url);
  return data.data;
}

export async function getInstructorsAndTeachingAssistants(
  courseId: string
): Promise<CourseStaff> {
  const url = getBITSEndApiMap("getCourseTeachingStaff").replace(
    ":courseId",
    courseId
  );
  const { data } = await api.get<CourseTeachingStaffResponse>(url);
  console.log("API called")
  return data.data;
}

// Generic function for sending FormData to any endpoint
export async function sendFormData<T = any>(
  endpoint: string,
  formData: FormData,
  pathParams?: Record<string, string | number>
): Promise<T> {
  let url = getBITSEndApiMap(endpoint);

  // Replace path parameters if provided
  if (pathParams) {
    url = buildUrl(url, pathParams);
  }

  const { data } = await api.post<T>(url, formData);
  console.log(`${endpoint} response`, data);
  return data;
}

// Checkpoint Management Hooks
export function useCoursesQuery(
  enabled = true
): UseQueryResult<Course[], Error> {
  return useQuery<Course[], Error>({
    queryKey: ["courses"],
    queryFn: getCourses, // getCourses() already returns Promise<Course[]>
    enabled,
    staleTime: 60_000,
  });
}

export function useStaffMemberQuery(
  courseId: string,
  enabled = true
): UseQueryResult<CourseStaff> {
  return useQuery({
    queryKey: ["courseStaff", courseId] as QueryKey,
    queryFn: () => getInstructorsAndTeachingAssistants(courseId),
    enabled: enabled && !!courseId,
    staleTime: 60_000,
  });
}

export function useCreateProjectMutation() {
  return useMutation({
    mutationFn: createProject,
  });
}
export function useEditProjectMutation() {
  return useMutation({
    mutationFn: ({ projectId, projectData }: { projectId: string; projectData: ProjectData }) =>
      editProject(projectId, projectData),
  });
}
export function useDatasetMutation() {
  return useMutation({
    mutationFn: (fd: FormData) => uploadDataset(fd),
  });
}

export function useCreateCheckPointMutation() {
  return useMutation({
    mutationFn: createCheckPoint,
  });
}

export function useCreateRubricMutation() {
  return useMutation({
    mutationFn: createRubric,
  });
}

// Generic hook for FormData mutations
//Usage:
// For a specific endpoint
// const formDataMutation = useFormDataMutation('someEndpoint');

// // For endpoints with path parameters
// const formDataMutation = useFormDataMutation('someEndpoint', { projectId: '123' });

// // Usage
// const formData = new FormData();
// formData.append('data', 'value');
// formDataMutation.mutate(formData);
export function useFormDataMutation<T = any>(
  endpoint: string,
  pathParams?: Record<string, string | number>
) {
  return useMutation({
    mutationFn: (formData: FormData) =>
      sendFormData<T>(endpoint, formData, pathParams),
  });
}
// 🔹 API function
export async function updateRubric(
  rubricData: GradingRubric[]
): Promise<CreateRubricResponse> {
  const url = getBITSEndApiMap("updateRubric");
  const { data } = await api.put<CreateRubricResponse>(url, rubricData);
  console.log("update rubric response", data);
  return data;
}

// 🔹 React Query mutation hook
export function useUpdateRubricMutation() {
  return useMutation({
    mutationFn: updateRubric,
  });
}
