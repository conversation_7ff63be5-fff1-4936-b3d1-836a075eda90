import React, { useEffect, useState, createContext, useContext } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "./components/ui/card";
import { Button } from "./components/ui/button";
import { Input } from "./components/ui/input";
import { Label } from "./components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "./components/ui/select";
import { Toaster } from "./components/ui/sonner";
import { toast } from "sonner";
import { ImageWithFallback } from "./components/figma/ImageWithFallback";
import { SandboxSessionProvider } from "./contexts/SandboxSessionContext";
import { SubmissionNavigationProvider } from "./contexts/SubmissionNavigationContext";
import {
  Home,
  Users,
  BookOpen,
  GraduationCap,
  Settings,
  FolderOpen,
  BarChart3,
  Shield,
  User,
  FileText,
  MessageSquare,
  Terminal,
  Code,
  UserPlusIcon,
  Clock,
  ChevronDown,
  ChevronRight,
} from "lucide-react";
import StudentDashboard from "./pages/StudentDashboard";
import StudentsAllProjectsPage from "./pages/StudentsAllProjectsPage";
import InstructorDashboard from "./pages/InstructorDashboard";
import AdminDashboard from "./pages/AdminDashboard";
import ProjectDetailsPage from "./pages/ProjectDetailsPage";
import SubmissionsPage from "./pages/SubmissionsPage";
import CoursesPage from "./pages/CoursesPage";
import ViewCoursePage from "./pages/ViewCoursePage";
import ViewAllProjectsPage from "./pages/ViewAllProjectsPage";
import InstructorProjectDetailsPage from "./pages/InstructorProjectDetailsPage";
import ViewStudentPage from "./pages/ViewStudentPage";
import GradebookPage from "./pages/GradebookPage";
// import GradeDetailsPage from "./components/GradeDetailsPage";
import AdminProjectsPage from "./pages/AdminProjectsPage";
import AdminReportsPage from "./pages/AdminReportsPage";
import AdminSettingsPage from "./pages/AdminSettingsPage";
import AuditPage from "./pages/AuditPage";
import CommunicationPage from "./pages/CommunicationPage";
import SandboxEnvironmentPage from "./pages/SandboxEnvironmentPage";
import SandboxManagementPage from "./pages/SandboxManagementPage";
import UserManagementPage from "./pages/UserManagementPage";
import GradingPage from "./pages/GradingPage";
import EnhancedSubmissionDetailsPage from "./pages/EnhancedSubmissionDetailsPage";
// import SubmissionDetailsPage from "./components/SubmissionDetailsPage";
import ProjectManagementPage from "./pages/ProjectManagementPage";
import CreateProjectPage from "./pages/CreateProjectPage";
import ModelTestingPage from "./pages/ModelTestingPage";
import NotebookPage from "./pages/NotebookPage";
import { JupyterWorkspaceProvider } from "./contexts/JupyterWorkspaceContext";
import NotebooksPage from "./pages/NotebooksPage";
import CodeViewerPage from "./pages/CodeViewerPage";
import DatasetsPage from "./pages/DatasetsPage";
import Header from "./components/Header";
import RolesPermissionsListPage from "./pages/RolesPermissionsListPage";
import SandboxAnalyticsDashboard from "./pages/SandboxAnalyticsDashboard";
import TimeExtensionManagement from "./components/TimeExtensionManagement";
import appLogo from "./assets/bits-logo.png";
import PythonScriptsPage from "./pages/PythonScriptsPage";
import { useLoginQuery, logoutUser } from "./api/authApi";
import { tokenService } from "./services/TokenService";
import { useJupyterWorkspace } from "../src/contexts/JupyterWorkspaceContext";
import { getPrimaryRoleName } from "./utils/authUtils";
import paths from "./routes";
import studentRoutes from "./StudentRoutes";
import {
  Routes,
  Route,
  Navigate,
  useNavigate,
  useLocation,
} from "react-router-dom";
type UserRole = "student" | "instructor" | "admin";

interface User {
  id: string;
  name: string;
  email: string;
  role: UserRole;
}

interface AuthContextType {
  user: User | null;
  login: (email: string, password: string, role: UserRole) => Promise<boolean>;
  logout: () => void;
  setUser: React.Dispatch<React.SetStateAction<User | null>>;
}

interface NavigationContextType {
  currentPage: string;
  navigateTo: (page: string, params?: any) => void;
  pageParams: any;
  goBack: () => void;
  navigationHistory: Array<{ page: string; params: any }>;
}

interface UtilityContextType {
  downloadSubmission: (submissionId: string, submissionData: any) => void;
  generateSubmissionZip: (submissionData: any) => Promise<Blob>;
}

// Mock users for demo
const mockUsers: { [key: string]: User } = {
  "<EMAIL>": {
    id: "1",
    name: "sharma",
    email: "<EMAIL>",
    role: "student",
  },
  "<EMAIL>": {
    id: "4",
    name: "rohan",
    email: "<EMAIL>",
    role: "student",
  },
  "<EMAIL>": {
    id: "2",
    name: "Dr. A. Sharma",
    email: "<EMAIL>",
    role: "instructor",
  },
  "<EMAIL>": {
    id: "3",
    name: "Admin User",
    email: "<EMAIL>",
    role: "admin",
  },
};

// Demo credentials for easy login
const demoCredentials = [
  {
    title: "Admin User",
    email: "<EMAIL>",
    password: "password",
    role: "admin" as UserRole,
  },
  {
    title: "Instructor",
    email: "<EMAIL>",
    password: "password",
    role: "instructor" as UserRole,
  },
  {
    title: "Sharma",
    email: "<EMAIL>",
    password: "password",
    role: "student" as UserRole,
  },
  {
    title: "Rohan",
    email: "<EMAIL>",
    password: "password",
    role: "student" as UserRole,
  },
];

// Auth Context
const AuthContext = createContext<AuthContextType | null>(null);

// Navigation Context
const NavigationContext = createContext<NavigationContextType | null>(null);

// Utility Context
const UtilityContext = createContext<UtilityContextType | null>(null);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

export const useNavigation = () => {
  const context = useContext(NavigationContext);
  if (!context) {
    throw new Error("useNavigation must be used within a NavigationProvider");
  }
  return context;
};

export const useUtilities = () => {
  const context = useContext(UtilityContext);
  if (!context) {
    throw new Error("useUtilities must be used within a UtilityProvider");
  }
  return context;
};

// Utility functions for download functionality
const downloadSubmission = (submissionId: string, submissionData: any) => {
  // Create a zip file with submission contents
  generateSubmissionZip(submissionData)
    .then((blob) => {
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `submission_${submissionId}_${
        submissionData.assignmentTitle || "assignment"
      }.zip`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      toast.success("Submission downloaded successfully");
    })
    .catch((error) => {
      console.error("Download failed:", error);
      toast.error("Failed to download submission");
    });
};

const generateSubmissionZip = async (submissionData: any): Promise<Blob> => {
  // In a real implementation, this would use a library like JSZip
  // For now, we'll create a mock zip file with submission metadata
  const mockZipContent = {
    metadata: {
      submissionId: submissionData.id,
      assignmentTitle: submissionData.assignmentTitle,
      studentName: submissionData.studentName,
      submissionDate: submissionData.submissionDate,
      status: submissionData.status,
      grade: submissionData.grade,
      feedback: submissionData.feedback,
    },
    files: submissionData.files || [],
    notebooks: submissionData.notebooks || [],
    datasets: submissionData.datasets || [],
  };

  // Create a JSON file as a placeholder for the actual zip
  const jsonContent = JSON.stringify(mockZipContent, null, 2);
  return new Blob([jsonContent], { type: "application/json" });
};

// Main Sidebar Component
function MainSidebar() {
  const { user } = useAuth();
  const { currentPage, navigateTo } = useNavigation();
  const navigate = useNavigate();
  const location = useLocation();
  const [expandedMenus, setExpandedMenus] = useState<Record<string, boolean>>(
    {}
  );

  if (!user) return null;

  const toggleMenu = (menuId: string) => {
    setExpandedMenus((prev) => ({
      ...prev,
      [menuId]: !prev[menuId],
    }));
  };

  const getMenuItems = () => {
    if (user.role === "admin") {
      return [
        { id: "dashboard", label: "Dashboard", icon: Home, path: "/" },
        {
          id: "user-management",
          label: "Users",
          icon: Users,
          path: "/user-management",
        },
        {
          id: "role-permissions",
          label: "Role Permissions",
          icon: UserPlusIcon,
          path: "/role-permissions",
        },
        { id: "courses", label: "Courses", icon: BookOpen, path: "/courses" },
        {
          id: "gradebook",
          label: "Gradebook",
          icon: GraduationCap,
          path: "/gradebook",
        },
        {
          id: "sandbox-management",
          label: "Sandbox Management",
          icon: Terminal,
          expandable: true,
          submenu: [
            {
              id: "sandbox-management",
              label: "Overview",
              icon: Terminal,
              path: "/sandbox-management",
            },
            {
              id: "time-extensions",
              label: "Time Extensions",
              icon: Clock,
              path: "/time-extensions",
            },
          ],
        },
      ];
    } else if (user.role === "instructor") {
      return {
        primary: [
          { id: "dashboard", label: "Dashboard", icon: Home, path: "/" },
          { id: "courses", label: "Courses", icon: BookOpen, path: "/courses" },
          {
            id: "gradebook",
            label: "Gradebook",
            icon: GraduationCap,
            path: "/gradebook",
          },
          {
            id: "submissions",
            label: "Submissions",
            icon: FileText,
            path: "/submissions",
          },
          {
            id: "communication",
            label: "Communication",
            icon: MessageSquare,
            path: "/communication",
          },
        ],
        management: [
          {
            id: "manage-projects",
            label: "Manage Projects",
            icon: FolderOpen,
            path: "/app/manage-projects",
          },
          {
            id: "user-management",
            label: "Students",
            icon: Users,
            path: "/user-management",
          },
          {
            id: "sandbox-management",
            label: "Sandbox Management",
            icon: Terminal,
            expandable: true,
            submenu: [
              {
                id: "sandbox-management",
                label: "Overview",
                icon: Terminal,
                path: "/sandbox-management",
              },
              {
                id: "time-extensions",
                label: "Time Extensions",
                icon: Clock,
                path: "/time-extensions",
              },
            ],
          },
        ],
      };
    } else {
      return [
        { id: "dashboard", label: "Dashboard", icon: Home, path: "/" },
        { id: "courses", label: "Courses", icon: BookOpen, path: "/courses" },
        // { id: "notebook", label: "Notebook", icon: Code, path: "/notebooks" },
        {
          id: "submissions",
          label: "Submissions",
          icon: FileText,
          path: "/submissions",
        },
        {
          id: "gradebook",
          label: "Grades",
          icon: GraduationCap,
          path: "/gradebook",
        },
        { id: "sandbox", label: "Sandbox", icon: Terminal, path: "/sandbox" },
      ];
    }
  };

  const adminToolsItems = [
    {
      id: "manage-projects",
      label: "Manage Projects",
      icon: FolderOpen,
      path: "/app/manage-projects",
    },
    {
      id: "sandbox-analytics",
      label: "Sandbox Analytics",
      icon: BarChart3,
      path: "/sandbox-analytics",
    },
    {
      id: "admin-reports",
      label: "Reports & Analytics",
      icon: BarChart3,
      path: "/admin-reports",
    },
    {
      id: "admin-audit",
      label: "Audit Logs",
      icon: Shield,
      path: "/admin-audit",
    },
    {
      id: "admin-settings",
      label: "System Settings",
      icon: Settings,
      path: "/admin-settings",
    },
  ];

  const renderMenuItem = (item: any, isSubmenu = false) => {
    const isActive = location.pathname === item.path;
    const isExpanded = expandedMenus[item.id];

    return (
      <li key={item.id} className={isSubmenu ? "ml-4" : ""}>
        <button
          onClick={() => {
            if (item.expandable) {
              toggleMenu(item.id);
            } else {
              console.log(item.path);
              navigate(item.path);
              //navigateTo(item.id);
            }
          }}
          className={`
            w-full flex items-center px-3 py-2 text-sm rounded-md transition-colors
            ${
              isActive
                ? "bg-bits-blue text-white"
                : "text-gray-600 hover:bg-gray-100"
            }
            ${isSubmenu ? "text-xs pl-6" : ""}
          `}
        >
          <item.icon className={`${isSubmenu ? "w-3 h-3" : "w-4 h-4"} mr-3`} />
          <span className="flex-1 text-left">{item.label}</span>
          {item.expandable && (
            <div className="ml-auto">
              {isExpanded ? (
                <ChevronDown className="w-4 h-4" />
              ) : (
                <ChevronRight className="w-4 h-4" />
              )}
            </div>
          )}
        </button>

        {item.expandable && item.submenu && isExpanded && (
          <ul className="space-y-1 mt-1">
            {item.submenu.map((subItem: any) => renderMenuItem(subItem, true))}
          </ul>
        )}
      </li>
    );
  };

  const menuItems: any = getMenuItems();

  return (
    // <div className="fixed left-0 z-40 bg-white border-gray-200 w-64 bg-sidebar border-r border-sidebar-border flex flex-col flex-shrink-0 z-30">
    <div className="fixed left-0 z-40 bg-white w-64 border-r border-gray-200 flex flex-col flex-shrink-0">
      {/* Navigation */}
      <nav className="flex-1 px-4 py-6 overflow-y-auto">
        {user.role === "instructor" ? (
          // Instructor navigation with two sections
          <>
            {/* Primary Teaching Functions */}
            <div className="px-3 py-2 text-xs font-semibold text-gray-400 uppercase tracking-wider">
              Teaching & Grading
            </div>
            <ul className="space-y-1 mt-2">
              {menuItems.primary.map((item: any) => renderMenuItem(item))}
            </ul>

            {/* Management Functions */}
            <div className="px-3 py-2 text-xs font-semibold text-gray-400 uppercase tracking-wider mt-6">
              Course Management
            </div>
            <ul className="space-y-1 mt-2">
              {menuItems.management.map((item: any) => renderMenuItem(item))}
            </ul>
          </>
        ) : (
          // Regular navigation for admin and student roles
          <ul className="space-y-1">
            {(Array.isArray(menuItems) ? menuItems : []).map((item) =>
              renderMenuItem(item)
            )}
          </ul>
        )}

        {user.role === "admin" && (
          <>
            <div className="px-3 py-2 text-xs font-semibold text-gray-400 uppercase tracking-wider mt-6">
              Admin Tools
            </div>
            <ul className="space-y-1 mt-2">
              {adminToolsItems.map((item) => renderMenuItem(item))}
            </ul>
          </>
        )}
      </nav>
    </div>
  );
}

// Login Component
function LoginForm() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [role, setRole] = useState<UserRole>("student");
  const [loading, setLoading] = useState(false);
  const { login, setUser } = useAuth();
  const loginQuery = useLoginQuery({ email, password });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      // Use backend login first
      const result = await loginQuery.refetch();
      const resp = result.data as any;

      if (resp?.isSuccess && resp?.data?.accessToken && resp?.data?.user) {
        // Save token with expiry for subsequent requests
        tokenService.setToken(resp.data.accessToken, resp.data.expiresIn);

        // Map backend role to local role using utility function
        const roleName = getPrimaryRoleName(resp.data.user.roles);
        const mappedRole: UserRole =
          roleName === "admin"
            ? "admin"
            : roleName === "instructor" || roleName === "teacher"
            ? "instructor"
            : "student";

        const newUser: User = {
          id: resp.data.user.id,
          name: resp.data.user.name,
          email: resp.data.user.email,
          role: mappedRole,
        };
        setUser(newUser);
        try {
          localStorage.setItem("auth_user", JSON.stringify(newUser));
        } catch {}

        toast.success("Login successful!");
      } else {
        // Fallback to demo login if backend says invalid
        const success = await login(email, password, role);
        if (success) {
          toast.success("Login successful!");
        } else {
          toast.error(
            resp?.message || "Invalid credentials. Please try again."
          );
        }
      }
    } catch (error) {
      toast.error("Login failed. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleCredentialClick = (credential: (typeof demoCredentials)[0]) => {
    setEmail(credential.email);
    setPassword(credential.password);
    setRole(credential.role);
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-[#f7f8fa] py-16 px-4">
      <div className="flex flex-col sm:flex-row flex-nowrap items-start justify-center gap-6">
        {/* Login Form Card */}
        <div className="w-full max-w-md">
          <Card className="w-full bg-white rounded-2xl shadow-lg p-8 border border-border">
            <CardHeader className="text-center pb-2">
              <div className="mb-4">
                <div className="w-20 h-20 mx-auto mb-4 relative">
                  <ImageWithFallback
                    src={appLogo}
                    alt="BITS Pilani Logo"
                    className="w-full h-full object-contain"
                  />
                </div>
              </div>
              <CardTitle className="text-bits-blue text-xl font-semibold mb-1">
                BITS Pilani Digital
              </CardTitle>
              <CardDescription className="text-base text-muted-foreground">
                Data Science Project Platform
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-2">
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="space-y-2 text-left">
                  <Label htmlFor="email" className="text-sm font-medium">
                    Email
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="Enter your email"
                    required
                    className="bg-muted rounded-lg text-muted-foreground border-none focus:ring-2 focus:ring-bits-blue focus:border-bits-blue"
                  />
                </div>
                <div className="space-y-2 text-left">
                  <Label htmlFor="password" className="text-sm font-medium">
                    Password
                  </Label>
                  <Input
                    id="password"
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="Enter your password"
                    required
                    className="bg-muted rounded-lg text-muted-foreground border-none focus:ring-2 focus:ring-bits-blue focus:border-bits-blue"
                  />
                </div>
                <div className="space-y-2 text-left">
                  <Label htmlFor="role" className="text-sm font-medium">
                    Role
                  </Label>
                  <Select
                    value={role}
                    onValueChange={(value: UserRole) => setRole(value)}
                  >
                    <SelectTrigger className="bg-muted rounded-lg border-none focus:ring-2 focus:ring-bits-blue focus:border-bits-blue">
                      <SelectValue placeholder="Select your role" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="student">Student</SelectItem>
                      <SelectItem value="instructor">Instructor</SelectItem>
                      <SelectItem value="admin">Administrator</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <Button
                  type="submit"
                  variant="bits"
                  className="w-full rounded-lg py-2 mt-2 shadow-sm text-base font-semibold"
                  disabled={loading}
                >
                  {loading ? "Signing in..." : "Sign In"}
                </Button>
              </form>
            </CardContent>
          </Card>
        </div>

        {/* Demo Credentials Card */}
        <div className="w-full max-w-md">
          <Card className="w-full shadow-md rounded-lg">
            <CardHeader className="p-8 pb-4">
              <CardTitle className="text-base font-semibold">
                Demo Credentials
              </CardTitle>
              <CardDescription className="text-sm text-muted-foreground">
                Use these credentials to explore different user roles
              </CardDescription>
            </CardHeader>
            <CardContent className="p-8 pt-4 space-y-3">
              {demoCredentials.map((credential, index) => (
                <div
                  key={index}
                  onClick={() => handleCredentialClick(credential)}
                  className="p-3 border border-border rounded-lg cursor-pointer hover:border-bits-blue hover:bg-bits-light-blue/10 transition-colors mb-2"
                >
                  <h4 className="text-xs font-semibold text-foreground mb-0">
                    {credential.title}
                  </h4>
                  <p className="text-xs text-muted-foreground mb-0">
                    {credential.email}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    Password: {credential.password}
                  </p>
                </div>
              ))}
              <div className="mt-2 p-3 bg-muted rounded-lg">
                <p className="text-xs text-muted-foreground">
                  <strong>Tip:</strong> Click any credential above to auto-fill
                  the login form, or use any password with the listed emails.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
//   return (
//     <div className="min-h-screen flex items-center justify-center bg-[#f7f8fa] py-16 px-4">

//       <div className="w-full max-w-3xl mx-auto">
//       <div className="flex flex-row items-start justify-center gap-6 flex-wrap">
//       {/* <div className="w-full max-w-3xl">
//         <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center justify-center"> */}
//           {/* Login Form Card */}
//           <div className="w-full max-w-md mx-auto lg:mx-0">
//             <Card className="w-full bg-white rounded-2xl shadow-lg p-8 border border-border">
//               <CardHeader className="text-center pb-2">
//                 <div className="mb-4">
//                   <div className="w-20 h-20 mx-auto mb-4 relative">
//                     <ImageWithFallback
//                       src="https://upload.wikimedia.org/wikipedia/en/d/d3/BITS_Pilani-Logo.svg"
//                       alt="BITS Pilani Logo"
//                       className="w-full h-full object-contain"
//                     />
//                   </div>
//                 </div>
//                 <CardTitle className="text-bits-blue text-xl font-semibold mb-1">BITS Pilani Digital</CardTitle>
//                 <CardDescription className="text-base text-muted-foreground">Data Science Project Platform</CardDescription>
//               </CardHeader>
//               <CardContent className="pt-2">
//                 <form onSubmit={handleSubmit} className="space-y-4">
//                   <div className="space-y-2 text-left">
//                     <Label htmlFor="email" className="text-sm font-medium">Email</Label>
//                     <Input
//                       id="email"
//                       type="email"
//                       value={email}
//                       onChange={(e) => setEmail(e.target.value)}
//                       placeholder="Enter your email"
//                       required
//                       className="bg-muted rounded-lg text-muted-foreground border-none focus:ring-2 focus:ring-bits-blue focus:border-bits-blue"
//                     />
//                   </div>
//                   <div className="space-y-2 text-left">
//                     <Label htmlFor="password" className="text-sm font-medium">Password</Label>
//                     <Input
//                       id="password"
//                       type="password"
//                       value={password}
//                       onChange={(e) => setPassword(e.target.value)}
//                       placeholder="Enter your password"
//                       required
//                       className="bg-muted rounded-lg text-muted-foreground border-none focus:ring-2 focus:ring-bits-blue focus:border-bits-blue"
//                     />
//                   </div>
//                   <div className="space-y-2 text-left">
//                     <Label htmlFor="role" className="text-sm font-medium">Role</Label>
//                     <Select value={role} onValueChange={(value: UserRole) => setRole(value)}>
//                       <SelectTrigger className="bg-muted rounded-lg border-none focus:ring-2 focus:ring-bits-blue focus:border-bits-blue">
//                         <SelectValue placeholder="Select your role" />
//                       </SelectTrigger>
//                       <SelectContent>
//                         <SelectItem value="student">Student</SelectItem>
//                         <SelectItem value="instructor">Instructor</SelectItem>
//                         <SelectItem value="admin">Administrator</SelectItem>
//                       </SelectContent>
//                     </Select>
//                   </div>
//                   <Button
//                     type="submit"
//                     variant="bits"
//                     className="w-full rounded-lg py-2 mt-2 shadow-sm text-base font-semibold"
//                     disabled={loading}
//                   >
//                     {loading ? 'Signing in...' : 'Sign In'}
//                   </Button>
//                 </form>
//               </CardContent>
//             </Card>
//           </div>

//           {/* Demo Credentials Card */}
//           <div className="w-full max-w-md mx-auto lg:mx-0">
//             <Card className="w-full shadow-md rounded-lg">
//               <CardHeader className="p-8 pb-4">
//                 <CardTitle className="text-base font-semibold">Demo Credentials</CardTitle>
//                 <CardDescription className="text-sm text-muted-foreground">
//                   Use these credentials to explore different user roles
//                 </CardDescription>
//               </CardHeader>
//               <CardContent className="p-8 pt-4 space-y-3">
//                 {demoCredentials.map((credential, index) => (
//                   <div
//                     key={index}
//                     onClick={() => handleCredentialClick(credential)}
//                     className="p-3 border border-border rounded-lg cursor-pointer hover:border-bits-blue hover:bg-bits-light-blue/10 transition-colors mb-2"
//                   >
//                     <h4 className="text-xs font-semibold text-foreground mb-0">{credential.title}</h4>
//                     <p className="text-xs text-muted-foreground mb-0">{credential.email}</p>
//                     <p className="text-xs text-muted-foreground">Password: {credential.password}</p>
//                   </div>
//                 ))}

//                 <div className="mt-2 p-3 bg-muted rounded-lg">
//                   <p className="text-xs text-muted-foreground">
//                     <strong>Tip:</strong> Click any credential above to auto-fill the login form, or use any password with the listed emails.
//                   </p>
//                 </div>
//               </CardContent>
//             </Card>
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// }

// Main Dashboard Router
function DashboardRouter() {
  const { user } = useAuth();
  const { pageParams, goBack } = useNavigation();

  if (!user) return <Navigate to={paths.login} replace />;

  console.log(user);

  return (
    <Routes>
      <Route
        path="*"
        element={
          <Routes>
            {/* Common routes */}
            <Route
              path={paths.projectDetails}
              element={<ProjectDetailsPage />}
            />
            <Route path={paths.courses} element={<CoursesPage />} />
            <Route
              path={paths.viewCourse}
              element={<ViewCoursePage courseId={pageParams?.courseId} />}
            />
            <Route
              path={paths.viewAllProjects}
              element={<ViewAllProjectsPage />}
            />
            <Route
              path={paths.instructorProjectDetails}
              element={<InstructorProjectDetailsPage />}
            />
            <Route
              path={paths.viewStudent}
              element={<ViewStudentPage studentId={pageParams?.studentId} />}
            />
            <Route path={paths.gradebook} element={<GradebookPage />} />
            <Route
              path={paths.communication}
              element={
                <CommunicationPage defaultTab={pageParams?.defaultTab} />
              }
            />
            <Route
              path={paths.submissions}
              element={
                <SubmissionsPage assignmentId={pageParams?.assignmentId} />
              }
            />

            {/* Student-only routes */}
            {user.role === "student" &&
              studentRoutes.map(({ path, element, name, index }) => {
                const routeProps: any = { element };
                if (path) routeProps.path = path;
                if (index) routeProps.index = index;
                return <Route key={name} {...routeProps} />;
              })}

            {/* Instructor + Admin routes */}
            {(user.role === "instructor" || user.role === "admin") && (
              <>
                <Route
                  path={paths.timeExtensions}
                  element={<TimeExtensionManagement userRole={user.role} />}
                />
                <Route
                  path={paths.studentNotebook}
                  element={
                    <SandboxEnvironmentPage
                      mode="student-notebook"
                      studentId={pageParams?.studentId}
                      studentName={pageParams?.studentName}
                      assignmentId={pageParams?.assignmentId}
                      assignmentTitle={pageParams?.assignmentTitle}
                      notebookUrl={pageParams?.notebookUrl}
                    />
                  }
                />
                <Route
                  path={paths.modelTesting}
                  element={
                    <ModelTestingPage
                      studentId={pageParams?.studentId}
                      studentName={pageParams?.studentName}
                      assignmentId={pageParams?.assignmentId}
                      assignmentTitle={pageParams?.assignmentTitle}
                      submissionId={pageParams?.submissionId}
                      onBack={goBack}
                    />
                  }
                />
                <Route
                  path={paths.viewStudentCode}
                  element={
                    <CodeViewerPage
                      studentId={pageParams?.studentId}
                      studentName={pageParams?.studentName}
                      assignmentId={pageParams?.assignmentId}
                      assignmentTitle={pageParams?.assignmentTitle}
                      submissionId={pageParams?.submissionId}
                    />
                  }
                />
                <Route
                  path={paths.submissionDetails}
                  element={
                    <SubmissionNavigationProvider>
                      <EnhancedSubmissionDetailsPage />
                    </SubmissionNavigationProvider>
                  }
                />

                <Route
                  path={paths.userManagement}
                  element={<UserManagementPage />}
                />
                <Route
                  path={paths.sandboxManagement}
                  element={<SandboxManagementPage />}
                />
                <Route
                  path={paths.gradingSandbox}
                  element={<SandboxEnvironmentPage mode="grading" />}
                />
                <Route path={paths.grading} element={<GradingPage />} />
                <Route
                  path={paths.createProject}
                  element={<CreateProjectPage />}
                />
                <Route
                  path={paths.editProject}
                  element={<CreateProjectPage />}
                />
                <Route
                  path={paths.useTemplate}
                  element={<CreateProjectPage />}
                />
                <Route
                  path={paths.manageProjects}
                  element={<ProjectManagementPage />}
                />

                {user.role === "instructor" && (
                  <Route index element={<InstructorDashboard />} />
                )}
              </>
            )}

            {/* Admin-only routes */}
            {user.role === "admin" && (
              <>
                <Route
                  path={paths.rolePermissions}
                  element={<RolesPermissionsListPage />}
                />
                <Route
                  path={paths.sandboxAnalytics}
                  element={<SandboxAnalyticsDashboard />}
                />
                <Route
                  path={paths.adminProjects}
                  element={<AdminProjectsPage />}
                />
                <Route
                  path={paths.adminReports}
                  element={<AdminReportsPage />}
                />
                <Route
                  path={paths.adminSettings}
                  element={<AdminSettingsPage />}
                />
                <Route path={paths.adminAudit} element={<AuditPage />} />
                <Route index element={<AdminDashboard />} />
              </>
            )}

            <Route
              path={paths.sandbox}
              element={
                <SandboxEnvironmentPage initialTab={pageParams?.initialTab} />
              }
            />

            {/* Catch-all */}
            <Route
              path="*"
              element={<div className="mt-10 ml-4">Page not found</div>}
            />
          </Routes>
        }
      />
    </Routes>
  );
}

// Main App Component
export default function App() {
  const [user, setUser] = useState<User | null>(() => {
    try {
      const raw = localStorage.getItem("auth_user");
      return raw ? (JSON.parse(raw) as User) : null;
    } catch {
      return null;
    }
  });
  const [currentPage, setCurrentPage] = useState("dashboard");
  const [pageParams, setPageParams] = useState<any>(null);
  const [navigationHistory, setNavigationHistory] = useState<
    Array<{ page: string; params: any }>
  >([]);
  const { resetWorkspace } = useJupyterWorkspace();

  const login = async (
    email: string,
    password: string,
    role: UserRole
  ): Promise<boolean> => {
    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1000));

    const mockUser = mockUsers[email];
    if (mockUser && password === "password" && mockUser.role === role) {
      setUser(mockUser);
      return true;
    }
    return false;
  };
  useEffect(() => {
    if (!user) return;
    const path = window.location.pathname;
    if (path.includes("/api/project/manage-project")) {
      if (user.role === "admin" || user.role === "instructor") {
        navigateTo("manage-projects");
      } else if (user.role === "student") {
        navigateTo("student-dashboard");
      }
    }
  }, [user]);
  const logout = async () => {
    try {
      // Try server logout with current token
      const resp = await logoutUser();
      if (resp?.success) {
        toast.success(resp.message || "Logged out successfully");
      }
    } catch {
      // Ignore network/API errors; proceed with local cleanup
    } finally {
      setUser(null);
      resetWorkspace();
      tokenService.clearToken();
      try {
        localStorage.removeItem("auth_user");
      } catch {}
      setCurrentPage("dashboard");
      setPageParams(null);
      setNavigationHistory([]);
    }
  };

  const navigateTo = (page: string, params?: any) => {
    // Add current page to history before navigating (but not for sandbox launches to avoid clutter)
    if (currentPage !== "sandbox" || page !== "sandbox") {
      setNavigationHistory((prev) => [
        ...prev,
        { page: currentPage, params: pageParams },
      ]);
    }
    setCurrentPage(page);
    setPageParams(params);
  };

  const goBack = () => {
    if (navigationHistory.length > 0) {
      const previousPage = navigationHistory[navigationHistory.length - 1];
      console.log("click reached");
      setNavigationHistory((prev) => prev.slice(0, -1));
      if (previousPage) {
        setCurrentPage(previousPage.page);
        setPageParams(previousPage.params);
      } else {
        setCurrentPage("dashboard");
        setPageParams(null);
      }
    } else {
      // Default fallback to dashboard
      setCurrentPage("dashboard");
      setPageParams(null);
    }
  };

  const authValue: AuthContextType = {
    user,
    login,
    logout,
    setUser,
  };

  const navigationValue: NavigationContextType = {
    currentPage,
    navigateTo,
    pageParams,
    goBack,
    navigationHistory,
  };

  const utilityValue: UtilityContextType = {
    downloadSubmission,
    generateSubmissionZip,
  };

  // Initialize authentication on app load
  useEffect(() => {
    let cancelled = false;

    (async () => {
      // If we already have user details and a valid (non-expired) access token, skip refresh
      const hasValidAccessToken = !!tokenService.getToken();
      if (!cancelled && user && hasValidAccessToken) {
        // Nothing to do; user stays logged in and login form will not render
        return;
      }

      try {
        // Always attempt refresh on first app load
        const { refreshToken } = await import("./api/authApi");
        const refreshResp = await refreshToken();

        if (!cancelled && refreshResp?.success && refreshResp.user) {
          // Save the new access token with expiry
          tokenService.setToken(refreshResp.accessToken, refreshResp.expiresIn);

          // Map the simplified role structure from refresh response
          const roleName = (
            refreshResp.user.roles?.[0]?.name || "student"
          ).toLowerCase();
          const mappedRole: UserRole =
            roleName === "admin"
              ? "admin"
              : roleName === "instructor" || roleName === "teacher"
              ? "instructor"
              : "student";

          const refreshedUser: User = {
            id: refreshResp.user.id,
            name: refreshResp.user.name,
            email: refreshResp.user.email,
            role: mappedRole,
          };
          setUser(refreshedUser);
          try {
            localStorage.setItem("auth_user", JSON.stringify(refreshedUser));
          } catch {}

          // Redirect to dashboard on success
          navigateTo("dashboard");
          return;
        }

        // Treat non-success as failure: go to login
        if (!cancelled) {
          tokenService.clearToken();
          setUser(null);
          // Ensure we show the login form by landing on dashboard route
          setCurrentPage("dashboard");
          setPageParams(null);
          setNavigationHistory([]);
        }
      } catch (_error) {
        if (!cancelled) {
          // On error, clear any stale state and show login
          tokenService.clearToken();
          setUser(null);
          setCurrentPage("dashboard");
          setPageParams(null);
          setNavigationHistory([]);
        }
        return;
      }
    })();

    return () => {
      cancelled = true;
    };
  }, []);

  // return (
  //   <AuthContext.Provider value={authValue}>
  //     <NavigationContext.Provider value={navigationValue}>
  //       {/* Fixed Header on top */}
  //       <Header />
  //       {/* Fixed Sidebar below the header */}
  //       <div
  //         className="fixed left-0 z-40 bg-white border-r border-gray-200"
  //         style={{
  //           top: '72px',
  //           height: 'calc(100vh - 72px)',
  //           width: '16rem',
  //           marginTop: '0',
  //           position: 'fixed'
  //         }}
  //       >
  //         <MainSidebar />
  //       </div>
  //       {/* Main Content with left margin for sidebar and top padding for header */}
  //       <main className="ml-64 pt-[72px] min-h-screen bg-background p-6">
  //         {user ? (
  //           <DashboardRouter />
  //         ) : (
  //           <LoginForm />
  //         )}
  //         <Toaster />
  //       </main>
  //     </NavigationContext.Provider>
  //   </AuthContext.Provider>
  // );

  return (
    <AuthContext.Provider value={authValue}>
      <NavigationContext.Provider value={navigationValue}>
        <UtilityContext.Provider value={utilityValue}>
          <SandboxSessionProvider>
            <JupyterWorkspaceProvider>
              {/* Fixed Header on top */}
              <Header />

              {/* Only render Sidebar when user is logged in */}
              {user && (
                <div
                  className="fixed left-0 z-40 bg-white border-r border-gray-200"
                  style={{
                    top: "72px",
                    height: "calc(100vh - 72px)",
                    width: "16rem",
                    marginTop: "0",
                    position: "fixed",
                  }}
                >
                  <MainSidebar />
                </div>
              )}

              {/* Adjust margin-left based on whether sidebar exists */}
              <main
                className={`${user ? "ml-64" : ""}  min-h-screen bg-background`}
                style={{ paddingTop: "52px" }}
              >
                {user ? <DashboardRouter /> : <LoginForm />}
                <Toaster />
              </main>
            </JupyterWorkspaceProvider>
          </SandboxSessionProvider>
        </UtilityContext.Provider>
      </NavigationContext.Provider>
    </AuthContext.Provider>
  );
}
