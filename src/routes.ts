// ./routes/index.ts
const paths = {
  // Auth
  login: "/login",

  // Common
  projectDetails: "/project-details/:projectId",
  toProjectDetails: (projectId: string) => `/project-details/${projectId}`,

  courses: "/courses",
  viewCourse: "/view-course/:courseId",
  toViewCourse: (courseId: string) => `/view-course/${courseId}`,

  viewAllProjects: "/view-all-project",
  instructorProjectDetails: "/view-project-details/:projectId",

  viewStudent: "/view-student/:studentId",
  toViewStudent: (studentId: string) => `/view-student/${studentId}`,

  gradebook: "/gradebook",
  communication: "/communication",
  submissions: "/submissions",

  // Student
  notebook: "/notebook/:projectId",
  notebooks: "/notebooks/:projectId",
  allProjects: "/allprojects",
  datasets: "/datasets/:projectId",
pythonScripts:'/scripts/:projectId',
  // Instructor/Admin
  timeExtensions: "/time-extensions",
  studentNotebook: "/student-notebook",
  modelTesting: "/model-testing",
  viewStudentCode: "/view-student-code",
  submissionDetails: "/submission-details",
  userManagement: "/user-management",
  sandboxManagement: "/sandbox-management",
  gradingSandbox: "/grading-sandbox",
  grading: "/grading",
  createProject: "/create-project",
  editProject: "/edit-project/:projectId",
  toEditProject: (projectId: string) => `/edit-project/${projectId}`,
  useTemplate: "/use-template",
  manageProjects: "/app/manage-projects",

  // Admin-only
  rolePermissions: "/role-permissions",
  sandboxAnalytics: "/sandbox-analytics",
  adminProjects: "/admin-projects",
  adminReports: "/admin-reports",
  adminSettings: "/admin-settings",
  adminAudit: "/admin-audit",

  // Sandbox (all roles)
  sandbox: "/sandbox",

  // Misc
  root: "/",
  notFound: "*",
};

export default paths;
